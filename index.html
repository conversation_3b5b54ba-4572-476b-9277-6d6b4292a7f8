<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>3D Planet Explorer - Real-time Sculpting</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div id="info">
    Click anywhere to enter pointer lock.<br>
    Controls: W A S D to move, SPACE to jump, mouse to look, V to toggle first/third person view.<br>
    Flashlight: F to toggle realistic flashlight with distance decay and battery life.<br>
    Performance: B for backface culling, C for terrain colors, G for GPU, J for aggressive culling, Q/E for view distance.<br>
    Visualization: K to toggle polygonal mesh overlay (shows marching cubes structure near player).<br>
    Resource Radar: R to toggle X-ray vision (see all resources through terrain).<br>
    Sculpting: Hold Left click to add, Hold Right click to remove. Keys 1/2/3 to change sculpt size.<br>
    Cleanup: Keys 7/8 to adjust small terrain threshold, 9 to toggle removal (current: 75 triangles).<br>
    Shadow Tuning: -/+ keys to adjust shadow bias (hold Shift for fine adjustment).<br>
    Wall Climbing: Move slowly to climb small walls. High walls require jumping.
  </div>

  <div id="loading-screen" style="
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    font-family: monospace;
    font-size: 18px;
    z-index: 1000;
  ">
    <div style="margin-bottom: 20px;">🌍 Generating Planet Terrain...</div>
    <div id="loading-progress" style="
      width: 300px;
      height: 20px;
      border: 2px solid #fff;
      border-radius: 10px;
      overflow: hidden;
      background: #333;
    ">
      <div id="loading-bar" style="
        height: 100%;
        background: linear-gradient(90deg, #4a7c59, #2d5016);
        width: 0%;
        transition: width 0.3s ease;
      "></div>
    </div>
    <div id="loading-text" style="margin-top: 10px;">Initializing voxel data...</div>
  </div>

  <!-- Resource Inventory Panel -->
  <div id="resource-panel">
    <h3>Resources</h3>
    <div class="resource-item">
      <span class="resource-icon coal-icon">⚫</span>
      <span class="resource-name">Coal:</span>
      <span id="coal-count" class="resource-count">0</span>
    </div>
    <div class="resource-item">
      <span class="resource-icon stone-icon">🪨</span>
      <span class="resource-name">Stone:</span>
      <span id="stone-count" class="resource-count">0</span>
    </div>
    <div class="resource-item">
      <span class="resource-icon gold-icon">🟡</span>
      <span class="resource-name">Gold:</span>
      <span id="gold-count" class="resource-count">0</span>
    </div>
    <div class="resource-item">
      <span class="resource-icon emerald-icon">🟢</span>
      <span class="resource-name">Emerald:</span>
      <span id="emerald-count" class="resource-count">0</span>
    </div>
  </div>

  <div id="perf-monitor">
    <div>FPS: <span id="fps">--</span></div>
    <div>Frame Time: <span id="frametime">--</span> ms</div>
    <div>Triangles: <span id="triangles">--</span></div>
    <div>Draw Calls: <span id="drawcalls">--</span></div>
    <div>Memory (JS): <span id="memory">--</span> MB</div>
    <div>Terrain Updates: <span id="terrain-updates">--</span></div>
    <div>Terrain Status: <span id="terrain-status">000</span></div>
    <div>Main Thread: <span id="main-thread-status" class="perf-good">Responsive</span></div>
    <div>Wall Climb: <span id="climb-status" class="perf-good">Available</span></div>
    <div>Flashlight: <span id="flashlight-status" class="perf-good">OFF</span></div>
    <div>Battery: <span id="battery-level" class="perf-good">100%</span></div>
    <div>Backface Cull: <span id="backface-status" class="perf-good">ON</span></div>
    <div>View Distance: <span id="view-distance">100</span></div>
    <div>Terrain Colors: <span id="terrain-colors" class="perf-good">ON</span></div>
    <div>GPU Compute: <span id="gpu-status" class="perf-good">ON</span></div>
    <div>Aggressive Culling: <span id="culling-status" class="perf-good">ON</span></div>
    <div>Small Terrain Threshold: <span id="terrain-threshold" class="perf-good">75</span></div>
    <div>Polygonal Mesh: <span id="polygonal-mesh-status" class="perf-good">OFF</span></div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/three@0.154.0/build/three.min.js"></script>
  <script src="marching_tables.js"></script>
  <script src="script.js"></script>
</body>
</html>