// Terrain Generation Web Worker
// This worker handles the computationally expensive marching cubes algorithm
// to prevent blocking the main thread during terrain sculpting.

// Import marching cubes lookup tables
importScripts('marching_tables.js');

console.log('Terrain worker loaded, edgeTable length:', edgeTable.length);

// These constants are now passed from the main thread for chunk generation
// const GRID_SIZE = 96; 
// const GRID_EXTENT = 60;
// const ISO_LEVEL = 0.0;

// Terrain elevation color levels (must match main thread, adjusted for proper surface level)
// Planet radius is now passed in as part of the data payload for color calcs.
const terrainLevels = (planetRadius) => ({
  grass:     { height: planetRadius + 2,    color: [0.29, 0.49, 0.35] }, // Green grass (RGB normalized)
  subGrass:  { height: planetRadius - 0.5,  color: [0.18, 0.31, 0.09] }, // Dark green sub grass
  lightDirt: { height: planetRadius - 3,    color: [0.82, 0.71, 0.55] }, // Light brown dirt
  darkDirt:  { height: planetRadius - 7,    color: [0.55, 0.27, 0.07] }, // Dark brown dirt
  lightStone:{ height: planetRadius - 13,   color: [0.75, 0.75, 0.75] }, // Light grey stone
  darkStone: { height: planetRadius - 21,   color: [0.41, 0.41, 0.41] }  // Dark grey stone
});

// Function to get color based on elevation
function getColorForElevation(y, levels) {
  if (y >= levels.grass.height) return levels.grass.color;
  if (y >= levels.subGrass.height) return levels.subGrass.color;
  if (y >= levels.lightDirt.height) return levels.lightDirt.color;
  if (y >= levels.darkDirt.height) return levels.darkDirt.color;
  if (y >= levels.lightStone.height) return levels.lightStone.color;
  return levels.darkStone.color;
}

// SDF sampling function with trilinear interpolation (matches main script)
function sampleSDF(pos, voxelData, gridSize, gridExtent, voxelSize) {
  const fx = (pos.x + gridExtent) / voxelSize;
  const fy = (pos.y + gridExtent) / voxelSize;
  const fz = (pos.z + gridExtent) / voxelSize;

  const x0 = Math.floor(fx), y0 = Math.floor(fy), z0 = Math.floor(fz);
  const x1 = x0 + 1, y1 = y0 + 1, z1 = z0 + 1;
  const tx = fx - x0, ty = fy - y0, tz = fz - z0;

  const clamp = v => Math.min(Math.max(v, 0), gridSize);
  const stride = gridSize + 1;
  const voxelIndex = (x, y, z) => x + y * stride + z * stride * stride;

  const v000 = voxelData[voxelIndex(clamp(x0), clamp(y0), clamp(z0))];
  const v100 = voxelData[voxelIndex(clamp(x1), clamp(y0), clamp(z0))];
  const v010 = voxelData[voxelIndex(clamp(x0), clamp(y1), clamp(z0))];
  const v110 = voxelData[voxelIndex(clamp(x1), clamp(y1), clamp(z0))];
  const v001 = voxelData[voxelIndex(clamp(x0), clamp(y0), clamp(z1))];
  const v101 = voxelData[voxelIndex(clamp(x1), clamp(y0), clamp(z1))];
  const v011 = voxelData[voxelIndex(clamp(x0), clamp(y1), clamp(z1))];
  const v111 = voxelData[voxelIndex(clamp(x1), clamp(y1), clamp(z1))];

  const v00 = v000 * (1 - tx) + v100 * tx;
  const v10 = v010 * (1 - tx) + v110 * tx;
  const v01 = v001 * (1 - tx) + v101 * tx;
  const v11 = v011 * (1 - tx) + v111 * tx;
  const v0 = v00 * (1 - ty) + v10 * ty;
  const v1 = v01 * (1 - ty) + v11 * ty;

  return v0 * (1 - tz) + v1 * tz;
}

// Calculate normal from SDF gradient - this gives much better lighting
function calculateNormalFromSDF(pos, voxelData, gridSize, gridExtent, voxelSize) {
  const e = 0.01; // Small epsilon for gradient calculation

  const dx = sampleSDF({x: pos.x + e, y: pos.y, z: pos.z}, voxelData, gridSize, gridExtent, voxelSize) -
             sampleSDF({x: pos.x - e, y: pos.y, z: pos.z}, voxelData, gridSize, gridExtent, voxelSize);
  const dy = sampleSDF({x: pos.x, y: pos.y + e, z: pos.z}, voxelData, gridSize, gridExtent, voxelSize) -
             sampleSDF({x: pos.x, y: pos.y - e, z: pos.z}, voxelData, gridSize, gridExtent, voxelSize);
  const dz = sampleSDF({x: pos.x, y: pos.y, z: pos.z + e}, voxelData, gridSize, gridExtent, voxelSize) -
             sampleSDF({x: pos.x, y: pos.y, z: pos.z - e}, voxelData, gridSize, gridExtent, voxelSize);

  const length = Math.sqrt(dx * dx + dy * dy + dz * dz);
  if (length > 0) {
    return { x: dx / length, y: dy / length, z: dz / length };
  }
  return { x: 0, y: 1, z: 0 }; // Default up normal
}

// Configurable threshold for removing small isolated terrain pieces
// Set to 0 to disable, or number of triangles (e.g., 75, 50, 25)
let SMALL_TERRAIN_THRESHOLD = 75;

// Remove small isolated terrain pieces to avoid floating debris
function removeSmallTerrainPieces(positions, normals, colors) {
  if (SMALL_TERRAIN_THRESHOLD <= 0 || positions.length === 0) {
    return { positions, normals, colors };
  }

  const triangleCount = positions.length / 9; // 3 vertices * 3 components per triangle
  if (triangleCount <= SMALL_TERRAIN_THRESHOLD) {
    // If entire chunk is below threshold, keep it (might be part of larger structure)
    return { positions, normals, colors };
  }

  // Build triangle connectivity graph
  const triangles = [];
  const vertexMap = new Map(); // Map vertex positions to triangle indices

  for (let i = 0; i < triangleCount; i++) {
    const triangle = {
      index: i,
      vertices: [],
      connected: new Set()
    };

    // Get the 3 vertices of this triangle
    for (let v = 0; v < 3; v++) {
      const baseIdx = i * 9 + v * 3;
      const vertex = {
        x: positions[baseIdx],
        y: positions[baseIdx + 1],
        z: positions[baseIdx + 2]
      };
      triangle.vertices.push(vertex);

      // Create vertex key for connectivity detection
      const vertexKey = `${vertex.x.toFixed(6)},${vertex.y.toFixed(6)},${vertex.z.toFixed(6)}`;
      if (!vertexMap.has(vertexKey)) {
        vertexMap.set(vertexKey, []);
      }
      vertexMap.get(vertexKey).push(i);
    }

    triangles.push(triangle);
  }

  // Build connectivity between triangles that share vertices
  for (const triangleIndices of vertexMap.values()) {
    for (let i = 0; i < triangleIndices.length; i++) {
      for (let j = i + 1; j < triangleIndices.length; j++) {
        const triA = triangleIndices[i];
        const triB = triangleIndices[j];
        triangles[triA].connected.add(triB);
        triangles[triB].connected.add(triA);
      }
    }
  }

  // Find connected components using flood fill
  const visited = new Set();
  const components = [];

  for (let i = 0; i < triangleCount; i++) {
    if (!visited.has(i)) {
      const component = [];
      const stack = [i];

      while (stack.length > 0) {
        const current = stack.pop();
        if (visited.has(current)) continue;

        visited.add(current);
        component.push(current);

        // Add connected triangles to stack
        for (const connected of triangles[current].connected) {
          if (!visited.has(connected)) {
            stack.push(connected);
          }
        }
      }

      components.push(component);
    }
  }

  // Filter out small components
  const keepTriangles = new Set();
  for (const component of components) {
    if (component.length >= SMALL_TERRAIN_THRESHOLD) {
      for (const triangleIndex of component) {
        keepTriangles.add(triangleIndex);
      }
    }
  }

  // Build filtered arrays
  const filteredPositions = [];
  const filteredNormals = [];
  const filteredColors = [];

  for (let i = 0; i < triangleCount; i++) {
    if (keepTriangles.has(i)) {
      const baseIdx = i * 9;

      // Copy triangle data
      for (let j = 0; j < 9; j++) {
        filteredPositions.push(positions[baseIdx + j]);
        filteredNormals.push(normals[baseIdx + j]);
        filteredColors.push(colors[baseIdx + j]);
      }
    }
  }

  return {
    positions: filteredPositions,
    normals: filteredNormals,
    colors: filteredColors
  };
}

// --- IMPROVED: This function now generates mesh for a specific chunk in WORLD COORDINATES ---
function generateChunkMesh(chunkData) {
    const {
        voxelData,
        chunkSize,
        worldOffset, // {x, y, z} world-space offset in voxels
        gridExtent,  // Global grid extent for coordinate calculation
        voxelSize,
        isoLevel,
        globalVoxelData, // Full voxel data for SDF sampling
        globalGridSize,  // Global grid size for SDF sampling
        smallTerrainThreshold // Threshold for removing small terrain pieces
    } = chunkData;

    // Update the global threshold for this chunk generation
    if (smallTerrainThreshold !== undefined) {
        SMALL_TERRAIN_THRESHOLD = smallTerrainThreshold;
    }

  const positions = [];
  const normals = [];
  const colors = [];

  const stride = chunkSize + 2; // Voxel data for a chunk includes a 1-voxel skirt

  function fastVoxelIndex(x, y, z) {
    return x + y * stride + z * stride * stride;
  }

  // Marching cubes vertex and edge definitions
  const vertexOffset = [
    [0,0,0],[1,0,0],[1,1,0],[0,1,0],
    [0,0,1],[1,0,1],[1,1,1],[0,1,1]
  ];

  const edgeConnection = [
    [0,1],[1,2],[2,3],[3,0],
    [4,5],[5,6],[6,7],[7,4],
    [0,4],[1,5],[2,6],[3,7]
  ];
  
  const T_LEVELS = terrainLevels(gridExtent / 1.5); // Derive planet radius

  // March through the chunk's volume (note: loop goes to chunkSize, not chunkSize+1)
  for (let z = 0; z < chunkSize + 1; z++) {
    for (let y = 0; y < chunkSize + 1; y++) {
      for (let x = 0; x < chunkSize + 1; x++) {

        let cubeIndex = 0;
        const cubeValues = new Array(8);
        const cubeVerts = new Array(8);

        let hasNegative = false;
        let hasPositive = false;

        for (let i = 0; i < 8; i++) {
          const vx = x + vertexOffset[i][0];
          const vy = y + vertexOffset[i][1];
          const vz = z + vertexOffset[i][2];
          const value = voxelData[fastVoxelIndex(vx, vy, vz)];
          cubeValues[i] = value;

          // --- CRITICAL FIX: Calculate vertex position in world space ---
          // 1. Get local voxel coordinates within the chunk data (including skirt)
          // 2. Add the chunk's world offset (in voxels)
          // 3. Convert from global voxel coordinates to world space
          const globalVx = worldOffset.x + vx - 1; // Subtract 1 to account for skirt
          const globalVy = worldOffset.y + vy - 1;
          const globalVz = worldOffset.z + vz - 1;

          cubeVerts[i] = {
            x: globalVx * voxelSize - gridExtent,
            y: globalVy * voxelSize - gridExtent,
            z: globalVz * voxelSize - gridExtent
          };

          if (value < isoLevel) {
            cubeIndex |= 1 << i;
            hasNegative = true;
          } else {
            hasPositive = true;
          }
        }
        
        if (!hasNegative || !hasPositive) continue;
        
        const edges = edgeTable[cubeIndex];
        if (!edges) continue;
        
        const edgeVerts = new Array(12);
        for (let i = 0; i < 12; i++) {
          if (edges & (1 << i)) {
            const [a0, b0] = edgeConnection[i];
            const valp1 = cubeValues[a0];
            const valp2 = cubeValues[b0];
            const p1 = cubeVerts[a0];
            const p2 = cubeVerts[b0];
            
            const mu = (isoLevel - valp1) / (valp2 - valp1);
            edgeVerts[i] = {
              x: p1.x + mu * (p2.x - p1.x),
              y: p1.y + mu * (p2.y - p1.y),
              z: p1.z + mu * (p2.z - p1.z)
            };
          }
        }
        
        const tri = triTable[cubeIndex];
        for (let i = 0; i < 16 && tri[i] !== -1; i += 3) {
          const a = edgeVerts[tri[i]];
          const b = edgeVerts[tri[i+1]];
          const c = edgeVerts[tri[i+2]];

          positions.push(c.x, c.y, c.z, b.x, b.y, b.z, a.x, a.y, a.z);

          // Calculate normals from SDF gradient for much better lighting
          let normalC, normalB, normalA;

          if (globalVoxelData && globalGridSize) {
            // Use SDF gradient normals for smooth lighting
            normalC = calculateNormalFromSDF(c, globalVoxelData, globalGridSize, gridExtent, voxelSize);
            normalB = calculateNormalFromSDF(b, globalVoxelData, globalGridSize, gridExtent, voxelSize);
            normalA = calculateNormalFromSDF(a, globalVoxelData, globalGridSize, gridExtent, voxelSize);
          } else {
            // Fallback to face normals if global data not available
            const cb = { x: b.x - c.x, y: b.y - c.y, z: b.z - c.z };
            const ca = { x: a.x - c.x, y: a.y - c.y, z: a.z - c.z };

            const normal = {
              x: cb.y * ca.z - cb.z * ca.y,
              y: cb.z * ca.x - cb.x * ca.z,
              z: cb.x * ca.y - cb.y * ca.x
            };

            const length = Math.sqrt(normal.x * normal.x + normal.y * normal.y + normal.z * normal.z);
            if (length > 0) {
              normal.x /= length;
              normal.y /= length;
              normal.z /= length;
            }

            normalC = normalB = normalA = normal;
          }

          // Add the calculated normals
          normals.push(normalC.x, normalC.y, normalC.z);
          normals.push(normalB.x, normalB.y, normalB.z);
          normals.push(normalA.x, normalA.y, normalA.z);

          const colorC = getColorForElevation(c.y, T_LEVELS);
          const colorB = getColorForElevation(b.y, T_LEVELS);
          const colorA = getColorForElevation(a.y, T_LEVELS);

          colors.push(colorC[0], colorC[1], colorC[2]);
          colors.push(colorB[0], colorB[1], colorB[2]);
          colors.push(colorA[0], colorA[1], colorA[2]);
        }
      }
    }
  }
  
  // Apply small terrain piece removal if threshold is set
  const cleanedMesh = removeSmallTerrainPieces(positions, normals, colors);

  return {
    positions: new Float32Array(cleanedMesh.positions),
    normals: new Float32Array(cleanedMesh.normals),
    colors: new Float32Array(cleanedMesh.colors)
  };
}


// Worker message handler
self.onmessage = function(e) {
  const { type, data } = e.data;
  
  switch (type) {
    case 'generateChunk':
      // Generate mesh data for the chunk
      const meshData = generateChunkMesh(data);

      // Send result back with chunk info
      self.postMessage({
        type: 'chunkGenerated',
        data: {
          meshData: meshData,
          chunkX: data.chunkX,
          chunkY: data.chunkY,
          chunkZ: data.chunkZ,
        }
      });
      break;
      
    default:
      console.warn('Unknown message type:', type);
  }
};

// Signal that worker is ready
self.postMessage({ type: 'workerReady' });