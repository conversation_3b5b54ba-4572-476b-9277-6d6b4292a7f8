// ====================================================================================
// ====================================================================================
// MODULE: config.js
// PURPOSE: Contains all static configuration, tuning parameters, and class definitions
// for game elements. Centralizing these values makes it easy to adjust game balance,
// performance, and behavior without searching through the entire codebase.
// ====================================================================================
// ====================================================================================

// --- Core Scene & Planet Parameters ---
const planetRadius = 40 / 1.5; // Made 1.5x smaller (26.67)
const gravity = 9.8;
const GRID_SIZE = 96;
const GRID_EXTENT = planetRadius * 1.5;
const VOXEL_SIZE = (GRID_EXTENT * 2) / GRID_SIZE;
const ISO_LEVEL = 0.0;

// --- Player Parameters ---
const playerHeight = 1.73;
const playerRadius = 0.33;
const jumpStrength = 5;
const walkSpeed = 5; // Note: This is now used as maxSpeed
const maxSpeed = 5;
const acceleration = 15;
const friction = 4;
const pitchLimit = Math.PI / 2 - 0.1;

// --- Player Collision Parameters ---
const collisionSamples = 12;
const collisionTolerance = 0.2;
const maxCorrectionPerFrame = 0.15;
const velocityDamping = 0.7;
const maxWalkableSlope = 0.6;
const smallWallThreshold = 0.07;
const maxClimbableHeight = 0.13;
const collisionHistorySize = 8;
const velocityHistorySize = 5;
const positionChangeThreshold = 5.0;

// --- Terrain Chunking System ---
const CHUNK_SIZE = 32;
const CHUNKS_PER_AXIS = Math.ceil(GRID_SIZE / CHUNK_SIZE);
let useChunkSystem = true; // Use chunks by default for performance

// --- Terrain Sculpting Parameters ---
const SCULPT_RADIUS_SMALL = 1.0;
const SCULPT_RADIUS_MID = 2.0;
const SCULPT_RADIUS_LARGE = 3.5;
const TERRAIN_MODIFICATION_STRENGTH = 0.9;
let SMALL_TERRAIN_THRESHOLD = 75; // Small terrain piece removal threshold (0 = disabled)
const minSculptInterval = 50; // Fastest sculpting rate
const sculptAcceleration = 2000; // Time to reach max speed (ms)
const sculptInterval = 100; // Milliseconds between sculpt operations

// --- Particle System Parameters & Classes ---
let miningParticles = [];
let smokeParticles = [];
const PARTICLE_POOL_SIZE = 200;
const SMOKE_POOL_SIZE = 300;

/**
 * Represents a geometric particle spawned when a resource is mined.
 * Each resource type can have a unique shape and material.
 */
class ResourceGeoParticle {
  constructor(position, color, resourceType, lifetime) {
    this.position = position.clone();
    this.velocity = new THREE.Vector3(
      (Math.random() - 0.5) * 6,
      Math.random() * 4 + 2,
      (Math.random() - 0.5) * 6
    );
    this.lifetime = lifetime;
    this.maxLifetime = lifetime;
    this.size = 0.1 + Math.random() * 0.15;
    this.color = new THREE.Color(color);
    this.resourceType = resourceType;
    this.rotationSpeed = new THREE.Vector3(
      (Math.random() - 0.5) * 10,
      (Math.random() - 0.5) * 10,
      (Math.random() - 0.5) * 10
    );

    this.createGeometry();
    this.mesh.position.copy(this.position);
    scene.add(this.mesh);
  }

  createGeometry() {
    let geometry;
    const resourceData = RESOURCE_TYPES[this.resourceType];
    switch (this.resourceType) {
      case 'COAL':    geometry = new THREE.OctahedronGeometry(this.size, 0); break;
      case 'STONE':   geometry = new THREE.BoxGeometry(this.size, this.size, this.size); break;
      case 'GOLD':    geometry = new THREE.ConeGeometry(this.size * 0.8, this.size * 1.5, 6); break;
      case 'EMERALD': geometry = new THREE.CylinderGeometry(this.size * 0.6, this.size * 0.6, this.size * 1.8, 6); break;
      default:        geometry = new THREE.SphereGeometry(this.size, 6, 4);
    }
    const material = new THREE.MeshStandardMaterial({
      color: this.color,
      metalness: resourceData.metallic,
      roughness: resourceData.roughness,
      transparent: true,
      opacity: 0.9
    });
    this.mesh = new THREE.Mesh(geometry, material);
  }

  update(deltaTime) {
    this.lifetime -= deltaTime;
    if (this.lifetime <= 0) {
      this.destroy();
      return false;
    }
    const particlePos = this.mesh.position.clone();
    const distanceToCenter = Math.max(particlePos.length(), 0.1);
    const gravityDirection = particlePos.clone().divideScalar(-distanceToCenter);
    // Use adaptive gravity for particles too
    const adaptiveGravity = calculateAdaptiveGravity(particlePos);
    this.velocity.add(gravityDirection.multiplyScalar(adaptiveGravity * deltaTime));
    this.velocity.multiplyScalar(0.98); // Air resistance
    this.position.add(this.velocity.clone().multiplyScalar(deltaTime));
    this.mesh.position.copy(this.position);
    this.mesh.rotation.x += this.rotationSpeed.x * deltaTime;
    this.mesh.rotation.y += this.rotationSpeed.y * deltaTime;
    this.mesh.rotation.z += this.rotationSpeed.z * deltaTime;
    this.mesh.material.opacity = (this.lifetime / this.maxLifetime) * 0.9;

    const sdfValue = sampleSDFAtPosition(this.mesh.position);
    if (sdfValue < 0.3) {
      const surfaceNormal = calculateSDFGradient(this.mesh.position);
      this.velocity.add(surfaceNormal.multiplyScalar(3.0));
      this.velocity.multiplyScalar(0.6); // Energy loss
      this.mesh.position.add(surfaceNormal.clone().multiplyScalar(0.3));
    }
    return true;
  }

  destroy() {
    if (this.mesh) {
      scene.remove(this.mesh);
      this.mesh.geometry.dispose();
      this.mesh.material.dispose();
      this.mesh = null;
    }
  }
}

/**
 * Represents a small smoke puff used for terrain sculpting effects.
 * Uses a simple plane geometry for performance.
 */
class SmokeParticle {
  constructor(position, color, lifetime) {
    this.position = position.clone();
    this.velocity = new THREE.Vector3((Math.random() - 0.5) * 1.5, Math.random() * 2 + 0.5, (Math.random() - 0.5) * 1.5);
    this.lifetime = lifetime;
    this.maxLifetime = lifetime;
    this.size = 0.05 + Math.random() * 0.1;
    this.color = new THREE.Color(color);

    const geometry = new THREE.PlaneGeometry(this.size, this.size);
    const material = new THREE.MeshBasicMaterial({ color: 0x888888, transparent: true, opacity: 0.4, side: THREE.DoubleSide });
    this.mesh = new THREE.Mesh(geometry, material);
    this.mesh.position.copy(this.position);
    scene.add(this.mesh);
  }

  update(deltaTime) {
    this.lifetime -= deltaTime;
    if (this.lifetime <= 0) {
      this.destroy();
      return false;
    }
    const particlePos = this.mesh.position.clone();
    const distanceToCenter = Math.max(particlePos.length(), 0.1);
    const gravityDirection = particlePos.clone().divideScalar(-distanceToCenter);
    // Use adaptive gravity for smoke particles too (very light)
    const adaptiveGravity = calculateAdaptiveGravity(particlePos);
    this.velocity.add(gravityDirection.multiplyScalar(adaptiveGravity * deltaTime * 0.1)); // Very light gravity
    this.velocity.y += deltaTime * 1.0; // Upward drift
    this.velocity.multiplyScalar(0.95); // Air resistance
    this.position.add(this.velocity.clone().multiplyScalar(deltaTime));
    this.mesh.position.copy(this.position);
    this.mesh.lookAt(camera.position); // Billboard effect
    const alpha = this.lifetime / this.maxLifetime;
    this.mesh.material.opacity = alpha * 0.4;
    this.mesh.scale.setScalar(1 + (1 - alpha) * 2.0); // Grow over time
    return true;
  }

  destroy() {
    if (this.mesh) {
      scene.remove(this.mesh);
      this.mesh.geometry.dispose();
      this.mesh.material.dispose();
      this.mesh = null;
    }
  }
}


// --- Flashlight System ---
const flashlightDecayRate = 0.5; // Battery drain per second
const flashlightRechargeRate = 0.5; // Battery recharge per second
const flashlightMinBattery = 5; // Minimum battery to turn on

// --- Performance Controls ---
let performanceMode = {
  backfaceCulling: true,
  nearClip: 0.1,
  farClip: 200,
  frustumCulling: true,
  aggressiveCulling: true,
  terrainColors: true,
  useGPU: false, // Disable GPU acceleration by default
  cullingDistance: 0.6 // Aggressive distance multiplier
};

// --- Polygonal Mesh Visualization ---
let polygonalMeshEnabled = false;
let polygonalMeshContainer = null;
let polygonalMeshRadius = 4; // Radius around player to generate mesh
let lastPolygonalMeshPosition = new THREE.Vector3();
let polygonalMeshUpdateThreshold = 1.25; // Distance player must move to update mesh
let polygonalMeshNeedsUpdate = false; // Flag to trigger update after sculpting
let lastPolygonalMeshUpdate = 0; // Timestamp of last update
let polygonalMeshUpdateCooldown = 250; // Minimum time between updates (ms)

// --- Enhanced Physics with Polygonal Mesh ---
let polygonalPhysicsData = null; // Stores high-resolution physics mesh data
let usePolygonalPhysics = true; // Enable enhanced physics when polygonal mesh is active

// --- Resource Mining System ---
const RESOURCE_TYPES = {
  COAL:    { name: 'Coal',    color: 0x2c2c2c, variations: [0x1a1a1a, 0x2c2c2c, 0x404040], metallic: 0.1, roughness: 0.9 },
  STONE:   { name: 'Stone',   color: 0x808080, variations: [0x606060, 0x808080, 0xa0a0a0], metallic: 0.0, roughness: 0.8 },
  GOLD:    { name: 'Gold',    color: 0xffd700, variations: [0xdaa520, 0xffd700, 0xffdf00], metallic: 0.9, roughness: 0.1 },
  EMERALD: { name: 'Emerald', color: 0x50c878, variations: [0x40a060, 0x50c878, 0x60d080], metallic: 0.3, roughness: 0.2 }
};

const RESOURCE_SIZES = {
  SMALL:  { scale: 0.7, health: 125, chunkCount: [2, 4], rarity: 0.5 },
  MEDIUM: { scale: 1.5, health: 175, chunkCount: [3, 6], rarity: 0.35 },
  LARGE:  { scale: 2.1, health: 250, chunkCount: [5, 9], rarity: 0.15 }
};

let resourceNodes = new Map(); // Stores all resource nodes by position key
let resourceChunks = []; // Active resource chunks that can be collected
let resourceInventory = { Coal: 0, Stone: 0, Gold: 0, Emerald: 0 };
let resourceContainer = new THREE.Object3D();
let chunkContainer = new THREE.Object3D();
let resourceSmokeParticles = []; // Separate array for resource mining smoke
let collectionParticles = []; // 2D particles for collection effects
let resourceUI = null;

// --- Resource Radar/X-Ray Vision System ---
let resourceRadarEnabled = false;
let resourceRadarMaterials = new Map(); // Store original materials for restoration

// --- Terrain Color Palette ---
const terrainLevels = {
  grass:      { height: planetRadius + 2,   color: 0x4a7c59 },
  subGrass:   { height: planetRadius - 0.5, color: 0x2d5016 },
  lightDirt:  { height: planetRadius - 3,   color: 0xd2b48c },
  darkDirt:   { height: planetRadius - 7,   color: 0x8b4513 },
  lightStone: { height: planetRadius - 13,  color: 0xc0c0c0 },
  darkStone:  { height: planetRadius - 21,  color: 0x696969 }
};

// ====================================================================================
// ====================================================================================
// END OF MODULE: config.js
// ====================================================================================
// ====================================================================================



// ====================================================================================
// ====================================================================================
// MODULE: sceneManager.js
// PURPOSE: Initializes and manages core Three.js components: scene, camera, renderer,
// and lights. Also includes helper classes for particle effects that are added to
// the scene.
// ====================================================================================
// ====================================================================================

let scene, camera, renderer;
let raycaster;

/**
 * Initializes the main Three.js scene, camera, and renderer.
 */
function initScene() {
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0x111111);
  scene.fog = new THREE.Fog(0x111111, 20, 80);

  camera = new THREE.PerspectiveCamera(90, window.innerWidth / window.innerHeight, performanceMode.nearClip, performanceMode.farClip);

  resourceContainer.name = 'resourceContainer';
  chunkContainer.name = 'chunkContainer';
  scene.add(resourceContainer);
  scene.add(chunkContainer);

  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(window.innerWidth, window.innerHeight);
  renderer.shadowMap.enabled = true;
  renderer.shadowMap.type = THREE.PCFSoftShadowMap;
  renderer.setClearColor(0x87CEEB, 1);
  document.body.appendChild(renderer.domElement);

  raycaster = new THREE.Raycaster();
}

/**
 * Sets up the lighting for the scene (ambient, directional, fill).
 */
function initLights() {
  const ambient = new THREE.AmbientLight(0x404040, 0.2);
  ambient.name = 'ambientLight';
  scene.add(ambient);

  const dirLight = new THREE.DirectionalLight(0xffffff, 1.0);
  dirLight.position.set(20, 27, 13);
  dirLight.castShadow = true;
  dirLight.shadow.mapSize.width = 2048;
  dirLight.shadow.mapSize.height = 2048;
  dirLight.shadow.camera.near = 1;
  dirLight.shadow.camera.far = 133;
  dirLight.shadow.camera.left = -67;
  dirLight.shadow.camera.right = 67;
  dirLight.shadow.camera.top = 67;
  dirLight.shadow.camera.bottom = -67;
  // Improved shadow bias to reduce artifacts in dark areas
  dirLight.shadow.bias = -0.0005;  // Less aggressive bias to reduce artifacts
  dirLight.shadow.normalBias = 0.05;  // Higher normal bias for better edge quality
  dirLight.shadow.radius = 6;  // Slightly higher radius for softer shadows
  scene.add(dirLight);

  const fillLight = new THREE.DirectionalLight(0x8888ff, 0.3);
  fillLight.position.set(-13, 13, -20);
  scene.add(fillLight);
}

/**
 * Adjusts shadow bias for debugging purposes. Can be called from the console.
 * @param {number} bias - The new shadow bias value.
 * @param {number} normalBias - The new shadow normal bias value.
 */
function tuneShadowBias(bias, normalBias) {
  const dirLight = scene.getObjectByProperty('isDirectionalLight', true);
  if (dirLight) {
    dirLight.shadow.bias = bias;
    dirLight.shadow.normalBias = normalBias;
    console.log(`Shadow bias updated: bias=${bias}, normalBias=${normalBias}`);
  }
  if (flashlight) {
    flashlight.shadow.bias = bias * 2.5;
    flashlight.shadow.normalBias = normalBias;
    console.log(`Flashlight bias updated: bias=${bias * 2.5}, normalBias=${normalBias}`);
  }
}
window.tuneShadowBias = tuneShadowBias; // Make accessible from console

/**
 * Handles window resize events to keep the camera and renderer updated.
 */
function onWindowResize() {
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(window.innerWidth, window.innerHeight);
}

/**
 * Represents a physical particle (e.g., a rock chip) for mining effects.
 * This class manages its own lifecycle and physics.
 */
class MiningParticle {
  constructor() {
    this.mesh = null;
    this.velocity = new THREE.Vector3();
    this.angularVelocity = new THREE.Vector3();
    this.life = 0;
    this.maxLife = 0;
    this.size = 0;
    this.active = false;
    this.bounces = 0;
    this.maxBounces = 3;
    this.lastSmokeTime = 0;
    this.smokeInterval = 50; // ms
  }

  init(position, isAdding) {
    this.active = true;
    this.life = 0;
    this.maxLife = 2000 + Math.random() * 1000;
    this.bounces = 0;
    this.lastSmokeTime = performance.now();
    const sizeType = Math.random();
    if (sizeType < 0.5) this.size = 0.05 + Math.random() * 0.05;
    else if (sizeType < 0.8) this.size = 0.1 + Math.random() * 0.1;
    else this.size = 0.2 + Math.random() * 0.15;

    if (!this.mesh) {
      const shapeType = Math.random();
      let geometry;
      if (shapeType < 0.4) geometry = new THREE.BoxGeometry(this.size, this.size * 0.8, this.size * 1.2);
      else if (shapeType < 0.7) geometry = new THREE.SphereGeometry(this.size * 0.6, 6, 4);
      else geometry = new THREE.ConeGeometry(this.size * 0.7, this.size * 1.5, 5);
      const material = new THREE.MeshLambertMaterial({ color: isAdding ? 0x8B4513 : 0x696969, transparent: true });
      this.mesh = new THREE.Mesh(geometry, material);
      scene.add(this.mesh);
    }

    this.mesh.position.copy(position);
    this.mesh.visible = true;
    this.mesh.material.opacity = 1.0;
    const speed = 3 + Math.random() * 4;
    const angle = Math.random() * Math.PI * 2;
    const elevation = Math.random() * Math.PI * 0.3;
    this.velocity.set(Math.cos(angle) * Math.cos(elevation) * speed, Math.sin(elevation) * speed, Math.sin(angle) * Math.cos(elevation) * speed);
    this.angularVelocity.set((Math.random() - 0.5) * 10, (Math.random() - 0.5) * 10, (Math.random() - 0.5) * 10);
  }

  update(deltaTime) {
    if (!this.active) return;
    this.life += deltaTime * 1000;

    const particlePos = this.mesh.position.clone();
    const distanceToCenter = Math.max(particlePos.length(), 0.1);
    const gravityDirection = particlePos.clone().divideScalar(-distanceToCenter);
    // Use adaptive gravity for mining particles
    const adaptiveGravity = calculateAdaptiveGravity(particlePos);
    this.velocity.add(gravityDirection.multiplyScalar(adaptiveGravity * deltaTime));
    this.mesh.position.add(this.velocity.clone().multiplyScalar(deltaTime));
    this.mesh.rotation.x += this.angularVelocity.x * deltaTime;
    this.mesh.rotation.y += this.angularVelocity.y * deltaTime;
    this.mesh.rotation.z += this.angularVelocity.z * deltaTime;

    const terrainHeight = sampleSDF(this.mesh.position);
    if (terrainHeight < this.size * 0.5 && this.bounces < this.maxBounces) {
      const normal = calculateSurfaceNormal(this.mesh.position);
      const bounceStrength = 0.6 - (this.bounces * 0.15);
      const dot = this.velocity.dot(normal);
      this.velocity.sub(normal.clone().multiplyScalar(2 * dot)).multiplyScalar(bounceStrength);
      this.mesh.position.add(normal.clone().multiplyScalar(this.size * 0.6));
      this.bounces++;
      this.angularVelocity.multiplyScalar(0.7);
    }

    if (performance.now() - this.lastSmokeTime > this.smokeInterval) {
      createSmokeParticle(this.mesh.position.clone());
      this.lastSmokeTime = performance.now();
    }

    const fadeStart = this.maxLife * 0.7;
    if (this.life > fadeStart) {
      this.mesh.material.opacity = 1.0 - ((this.life - fadeStart) / (this.maxLife - fadeStart));
    }

    if (this.life >= this.maxLife) this.deactivate();
  }

  deactivate() {
    this.active = false;
    if (this.mesh) this.mesh.visible = false;
  }
}

/**
 * Represents a 2D particle effect that travels across the screen to the UI.
 * Used for visual feedback when collecting resources.
 */
class CollectionParticle2D {
  constructor(startWorldPos, resourceType) {
    this.resourceType = resourceType;
    this.color = new THREE.Color(RESOURCE_TYPES[resourceType].color);
    this.lifetime = 1.8;
    this.maxLifetime = this.lifetime;

    const vector = startWorldPos.clone().project(camera);
    this.startX = (vector.x * 0.5 + 0.5) * window.innerWidth;
    this.startY = (-vector.y * 0.5 + 0.5) * window.innerHeight;
    this.targetX = this.getResourcePanelX(resourceType);
    this.targetY = this.getResourcePanelY(resourceType);
    this.currentX = this.startX;
    this.currentY = this.startY;

    this.element = document.createElement('div');
    this.element.style.cssText = `
      position: fixed; width: 12px; height: 12px; border-radius: 50%;
      background-color: #${this.color.getHexString()};
      box-shadow: 0 0 8px #${this.color.getHexString()}, 0 0 16px #${this.color.getHexString()}, 0 0 24px #${this.color.getHexString()}80;
      border: 2px solid #${this.color.getHexString()}; pointer-events: none; z-index: 10000;
      left: ${this.currentX}px; top: ${this.currentY}px; transition: none;
      filter: brightness(1.5) saturate(1.3); animation: pulse 0.5s ease-in-out infinite alternate;
    `;
    this.addPulseAnimation();
    document.body.appendChild(this.element);
  }

  getResourcePanelX(resourceType) { return 45; }
  getResourcePanelY(resourceType) {
    const baseY = 60, itemHeight = 40;
    switch(resourceType) {
      case 'COAL': return baseY;
      case 'STONE': return baseY + itemHeight;
      case 'GOLD': return baseY + (2 * itemHeight);
      case 'EMERALD': return baseY + (3 * itemHeight);
      default: return baseY;
    }
  }

  addPulseAnimation() {
    if (document.getElementById('pulse-animation')) return;
    const style = document.createElement('style');
    style.id = 'pulse-animation';
    style.textContent = `@keyframes pulse { 0% { transform: scale(1) rotate(0deg); } 100% { transform: scale(1.2) rotate(180deg); } }`;
    document.head.appendChild(style);
  }

  update(deltaTime) {
    this.lifetime -= deltaTime;
    if (this.lifetime <= 0) {
      this.destroy();
      return false;
    }
    const progress = 1 - (this.lifetime / this.maxLifetime);
    const midX = (this.startX + this.targetX) / 2;
    const midY = (this.startY + this.targetY) / 2 - 50; // Arc upward
    let t = progress < 0.5 ? progress * 2 : (progress - 0.5) * 2;
    this.currentX = progress < 0.5 ? this.startX + (midX - this.startX) * t : midX + (this.targetX - midX) * t;
    this.currentY = progress < 0.5 ? this.startY + (midY - this.startY) * t : midY + (this.targetY - midY) * t;
    this.element.style.left = this.currentX + 'px';
    this.element.style.top = this.currentY + 'px';
    this.element.style.opacity = Math.max(0, Math.min(1, progress < 0.9 ? 1.0 : (1.0 - progress) * 10));
    this.element.style.transform = `scale(${1.0 + Math.sin(progress * Math.PI) * 0.3}) rotate(${progress * 720}deg)`;
    this.element.style.filter = `brightness(${1.5 * (1 + Math.sin(progress * Math.PI * 4) * 0.5)}) saturate(1.5) contrast(1.2)`;
    return true;
  }

  destroy() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
      this.element = null;
    }
  }
}

/**
 * Represents a non-physical smoke particle for visual effects.
 * This class manages its own lifecycle and appearance.
 */
class MiningSmokeParticle {
  constructor() {
    this.mesh = null;
    this.velocity = new THREE.Vector3();
    this.life = 0;
    this.maxLife = 0;
    this.active = false;
    this.initialSize = 0;
    this.maxSize = 0;
  }

  init(position) {
    this.active = true;
    this.life = 0;
    this.maxLife = 1500 + Math.random() * 1000;
    this.initialSize = 0.02 + Math.random() * 0.03;
    this.maxSize = this.initialSize * (3 + Math.random() * 2);

    if (!this.mesh) {
      const geometry = new THREE.PlaneGeometry(1, 1);
      const material = new THREE.MeshBasicMaterial({ color: 0x888888, transparent: true, opacity: 0.3, side: THREE.DoubleSide });
      this.mesh = new THREE.Mesh(geometry, material);
      scene.add(this.mesh);
    }
    this.mesh.position.copy(position);
    this.mesh.visible = true;
    this.mesh.scale.setScalar(this.initialSize);
    this.mesh.material.opacity = 0.3;
    this.velocity.set((Math.random() - 0.5) * 0.5, 0.5 + Math.random() * 0.5, (Math.random() - 0.5) * 0.5);
  }

  update(deltaTime) {
    if (!this.active) return;
    this.life += deltaTime * 1000;
    const progress = this.life / this.maxLife;
    this.velocity.multiplyScalar(0.98);
    this.mesh.position.add(this.velocity.clone().multiplyScalar(deltaTime));
    this.mesh.scale.setScalar(this.initialSize + (this.maxSize - this.initialSize) * progress);
    this.mesh.material.opacity = 0.3 * (1.0 - progress);
    this.mesh.lookAt(camera.position);
    if (this.life >= this.maxLife) this.deactivate();
  }

  deactivate() {
    this.active = false;
    if (this.mesh) this.mesh.visible = false;
  }
}

/**
 * Initializes the particle pools for reuse, improving performance.
 */
function initMiningParticleSystem() {
  for (let i = 0; i < PARTICLE_POOL_SIZE; i++) miningParticles.push(new MiningParticle());
  for (let i = 0; i < SMOKE_POOL_SIZE; i++) smokeParticles.push(new MiningSmokeParticle());
}

/**
 * Creates a burst of mining particles from a pool.
 * @param {THREE.Vector3} position - The starting position for the particles.
 * @param {boolean} isAdding - Whether the terrain is being added or removed.
 * @param {number} count - The number of particles to create.
 */
function createMiningParticles(position, isAdding, count = 8) {
  for (let i = 0; i < count; i++) {
    const particle = miningParticles.find(p => !p.active);
    if (particle) {
      const offsetPos = position.clone().add(new THREE.Vector3((Math.random() - 0.5) * 0.5, (Math.random() - 0.5) * 0.5, (Math.random() - 0.5) * 0.5));
      particle.init(offsetPos, isAdding);
    }
  }
}

/**
 * Creates a single smoke particle from a pool.
 * @param {THREE.Vector3} position - The starting position for the particle.
 */
function createSmokeParticle(position) {
  const particle = smokeParticles.find(p => !p.active);
  if (particle) particle.init(position);
}

/**
 * Updates all active particles in the scene.
 * @param {number} deltaTime - The time elapsed since the last frame.
 */
function updateMiningParticles(deltaTime) {
  miningParticles.forEach(p => { if (p.active) p.update(deltaTime); });
  smokeParticles.forEach(p => { if (p.active) p.update(deltaTime); });
}

// ====================================================================================
// ====================================================================================
// END OF MODULE: sceneManager.js
// ====================================================================================
// ====================================================================================



// ====================================================================================
// ====================================================================================
// MODULE: uiManager.js
// PURPOSE: Handles all interactions with the DOM, including the performance monitor,
// loading screen, flashlight status, and other UI elements.
// ====================================================================================
// ====================================================================================

// --- Performance Monitoring State ---
let frameCount = 0;
let lastFPSUpdate = 0;
let currentFPS = 0;
let frameTimeHistory = [];
let terrainUpdateCount = 0;
let lastTerrainUpdate = 0;
const FPS_UPDATE_INTERVAL = 1000;
const FRAME_TIME_SAMPLES = 60;
let lastMainThreadCheck = 0;
let mainThreadResponsive = true;

/**
 * Updates the performance monitor overlay with current stats (FPS, frame time, etc.).
 * @param {number} deltaTime - The time elapsed since the last frame.
 */
function updatePerformanceMonitor(deltaTime) {
  frameCount++;
  const frameTimeMs = deltaTime * 1000;
  frameTimeHistory.push(frameTimeMs);
  if (frameTimeHistory.length > FRAME_TIME_SAMPLES) frameTimeHistory.shift();

  const now = performance.now();
  if (now - lastFPSUpdate >= FPS_UPDATE_INTERVAL) {
    currentFPS = Math.round(frameCount * 1000 / (now - lastFPSUpdate));
    frameCount = 0;
    lastFPSUpdate = now;

    const avgFrameTime = frameTimeHistory.reduce((a, b) => a + b, 0) / frameTimeHistory.length;
    const fpsElement = document.getElementById('fps');
    fpsElement.textContent = currentFPS;
    fpsElement.className = currentFPS >= 60 ? 'perf-good' : currentFPS >= 30 ? 'perf-warning' : 'perf-bad';
    document.getElementById('frametime').textContent = avgFrameTime.toFixed(2);

    let totalTriangles = 0;
    terrainContainer.children.forEach(chunk => {
      if (chunk.geometry) totalTriangles += chunk.geometry.attributes.position.count / 3;
    });
    document.getElementById('triangles').textContent = totalTriangles.toLocaleString();
    document.getElementById('drawcalls').textContent = scene.children.length;
    document.getElementById('memory').textContent = (performance.memory ? performance.memory.usedJSHeapSize / 1048576 : 0).toFixed(1);

    const terrainUpdateRate = terrainUpdateCount / ((now - lastTerrainUpdate) / 1000);
    document.getElementById('terrain-updates').textContent = terrainUpdateRate.toFixed(1) + '/s';
    terrainUpdateCount = 0;
    lastTerrainUpdate = now;
    
    const mainThreadElement = document.getElementById('main-thread-status');
    if (mainThreadElement) {
      mainThreadElement.textContent = mainThreadResponsive ? 'Responsive' : 'Blocked';
      mainThreadElement.className = mainThreadResponsive ? 'perf-good' : 'perf-bad';
    }
  }

  if (now - lastMainThreadCheck >= 100) {
    mainThreadResponsive = true;
    lastMainThreadCheck = now;
  }
}

/**
 * Updates the display for various performance-related toggles.
 */
function updatePerformanceDisplay() {
  const setStatus = (id, status, goodCondition) => {
    const el = document.getElementById(id);
    if (el) {
      el.textContent = status;
      el.className = goodCondition ? 'perf-good' : 'perf-warning';
    }
  };
  setStatus('backface-status', performanceMode.backfaceCulling ? 'ON' : 'OFF', performanceMode.backfaceCulling);
  setStatus('terrain-colors', performanceMode.terrainColors ? 'ON' : 'OFF', performanceMode.terrainColors);
  setStatus('gpu-status', performanceMode.useGPU ? 'ON' : 'OFF', performanceMode.useGPU);
  setStatus('culling-status', performanceMode.aggressiveCulling ? 'ON' : 'OFF', performanceMode.aggressiveCulling);
  setStatus('polygonal-mesh-status', polygonalMeshEnabled ? 'ON' : 'OFF', !polygonalMeshEnabled); // ON is a warning
  
  const viewDistEl = document.getElementById('view-distance');
  if(viewDistEl) {
    viewDistEl.textContent = Math.round(performanceMode.farClip);
    if (performanceMode.farClip <= 100) viewDistEl.className = 'perf-good';
    else if (performanceMode.farClip <= 200) viewDistEl.className = 'perf-warning';
    else viewDistEl.className = 'perf-bad';
  }
}

/**
 * Updates the flashlight status and battery level in the UI.
 */
function updateFlashlightDisplay() {
  const flashlightElement = document.getElementById('flashlight-status');
  const batteryElement = document.getElementById('battery-level');
  if (flashlightElement) {
    flashlightElement.textContent = flashlightOn ? 'ON' : 'OFF';
    flashlightElement.className = flashlightOn ? 'perf-warning' : 'perf-good';
  }
  if (batteryElement) {
    batteryElement.textContent = Math.round(flashlightBattery) + '%';
    if (flashlightBattery > 50) batteryElement.className = 'perf-good';
    else if (flashlightBattery > 20) batteryElement.className = 'perf-warning';
    else batteryElement.className = 'perf-bad';
  }
}

/**
 * Updates the UI to show whether the player can currently climb.
 */
function updateClimbStatus() {
    const climbElement = document.getElementById('climb-status');
    if (!climbElement) return;
    const currentSpeed = tangentVelocity.length();
    const maxClimbSpeed = 2.0;
    climbElement.textContent = currentSpeed <= maxClimbSpeed ? 'Available' : 'Too Fast';
    climbElement.className = currentSpeed <= maxClimbSpeed ? 'perf-good' : 'perf-warning';
}

/**
 * Updates the loading screen progress bar and text.
 * @param {number} percent - The percentage of completion (0-100).
 * @param {string} text - The text to display on the loading screen.
 */
function updateLoadingProgress(percent, text) {
  const loadingBar = document.getElementById('loading-bar');
  const loadingText = document.getElementById('loading-text');
  if (loadingBar) loadingBar.style.width = percent + '%';
  if (loadingText) loadingText.textContent = text;
}

/**
 * Hides the loading screen once the game is ready.
 */
function hideLoadingScreen() {
  const loadingScreen = document.getElementById('loading-screen');
  if (loadingScreen) loadingScreen.style.display = 'none';
}

// ====================================================================================
// ====================================================================================
// END OF MODULE: uiManager.js
// ====================================================================================
// ====================================================================================



// ====================================================================================
// ====================================================================================
// MODULE: inputManager.js
// PURPOSE: Centralizes all event listeners and user input handling (keyboard, mouse).
// It tracks input states and dispatches actions to other modules.
// ====================================================================================
// ====================================================================================

const keyStates = {};
let mouseButtons = { left: false, right: false };
let lastSculptTime = 0;
let sculptStartTime = 0;

/**
 * Sets up all the necessary event listeners for user input.
 */
function initInputListeners() {
  document.body.addEventListener('click', () => document.body.requestPointerLock());
  document.addEventListener('pointerlockchange', onPointerLockChange);
  document.addEventListener('mousedown', onMouseDown);
  document.addEventListener('mouseup', onMouseUp);
  document.addEventListener('contextmenu', (e) => e.preventDefault());
  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('keydown', onKeyDown);
  document.addEventListener('keyup', onKeyUp);
  window.addEventListener('resize', onWindowResize);
}

function onPointerLockChange() {
  document.getElementById('info').style.display = document.pointerLockElement === document.body ? 'none' : 'block';
}

function onMouseDown(event) {
  if (document.pointerLockElement !== document.body || (event.button !== 0 && event.button !== 2)) return;
  event.preventDefault();
  if (event.button === 0) mouseButtons.left = true;
  else if (event.button === 2) mouseButtons.right = true;
  if (!mouseButtons.left || !mouseButtons.right) sculptStartTime = performance.now();
  performSculpting();
  lastSculptTime = performance.now();
}

function onMouseUp(event) {
  if (document.pointerLockElement !== document.body || (event.button !== 0 && event.button !== 2)) return;
  event.preventDefault();
  if (event.button === 0) mouseButtons.left = false;
  else if (event.button === 2) mouseButtons.right = false;
}

function onMouseMove(event) {
  if (document.pointerLockElement !== document.body) return;
  yaw -= (event.movementX || 0) * 0.002;
  pitch -= (event.movementY || 0) * 0.002;
  pitch = Math.max(-pitchLimit, Math.min(pitchLimit, pitch));
  
  raycaster.setFromCamera({ x: 0, y: 0 }, camera);
  const inter = raycaster.intersectObject(terrainContainer, true);
  if (inter.length > 0) updateSculptGuide(inter[0].point, inter[0].face.normal);
  else hideSculptGuide();
}

function onKeyDown(e) {
  keyStates[e.code] = true;
  switch (e.code) {
    case 'KeyF': toggleFlashlight(); break;
    case 'KeyV': thirdPerson = !thirdPerson; break;
    case 'KeyB': toggleBackfaceCulling(); break;
    case 'KeyC': toggleTerrainColors(); break;
    case 'KeyG': toggleGPUCompute(); break;
    case 'KeyJ': toggleAggressiveCulling(); break;
    case 'KeyQ': adjustViewDistance(-10); break;
    case 'KeyE': adjustViewDistance(10); break;
    case 'KeyK': togglePolygonalMesh(); break;
    case 'KeyR': toggleResourceRadar(); break;
    case 'Minus': adjustShadowBias(e.shiftKey ? -0.0001 : -0.001); break;
    case 'Equal': adjustShadowBias(e.shiftKey ? 0.0001 : 0.001); break;
    case 'Digit1': currentSculptRadius = SCULPT_RADIUS_SMALL; break;
    case 'Digit2': currentSculptRadius = SCULPT_RADIUS_MID; break;
    case 'Digit3': currentSculptRadius = SCULPT_RADIUS_LARGE; break;
    case 'Digit7': adjustSmallTerrainThreshold(-1); break;
    case 'Digit8': adjustSmallTerrainThreshold(1); break;
    case 'Digit9': toggleSmallTerrainRemoval(); break;
  }
}

function onKeyUp(e) {
  keyStates[e.code] = false;
}

// --- Functions Called by InputManager ---

function toggleBackfaceCulling() {
  performanceMode.backfaceCulling = !performanceMode.backfaceCulling;
  updateTerrainMaterial();
  const ambient = scene.getObjectByName('ambientLight');
  if (ambient) ambient.intensity = performanceMode.backfaceCulling ? 0.25 : 0.2;
  updatePerformanceDisplay();
}

function adjustViewDistance(delta) {
  performanceMode.farClip = Math.max(50, Math.min(400, performanceMode.farClip + delta * 2));
  camera.far = performanceMode.farClip;
  camera.updateProjectionMatrix();
  updatePerformanceDisplay();
}

function toggleTerrainColors() {
  performanceMode.terrainColors = !performanceMode.terrainColors;
  updateTerrainMaterial();
  updatePerformanceDisplay();
}

function toggleGPUCompute() {
  performanceMode.useGPU = !performanceMode.useGPU;
  updatePerformanceDisplay();
}

function toggleAggressiveCulling() {
  performanceMode.aggressiveCulling = !performanceMode.aggressiveCulling;
  performanceMode.cullingDistance = performanceMode.aggressiveCulling ? 0.6 : 0.8;
  updatePerformanceDisplay();
}

function togglePolygonalMesh() {
  polygonalMeshEnabled = !polygonalMeshEnabled;
  console.log(`Polygonal mesh visualization: ${polygonalMeshEnabled ? 'ON' : 'OFF'}`);
  if (polygonalMeshEnabled) {
    initPolygonalMeshContainer();
    updatePolygonalMesh();
  } else {
    clearPolygonalMesh();
  }
  updatePerformanceDisplay();
}

function toggleResourceRadar() {
  resourceRadarEnabled = !resourceRadarEnabled;
  console.log(`Resource radar: ${resourceRadarEnabled ? 'ON' : 'OFF'}`);
  if (resourceRadarEnabled) enableResourceRadar();
  else disableResourceRadar();
}

function adjustShadowBias(delta) {
  const dirLight = scene.getObjectByProperty('isDirectionalLight', true);
  if (dirLight) {
    dirLight.shadow.bias += delta;
    dirLight.shadow.bias = Math.max(-0.01, Math.min(0.01, dirLight.shadow.bias));
    console.log(`Shadow bias adjusted: ${dirLight.shadow.bias.toFixed(6)}`);
  }
  if (flashlight) {
    flashlight.shadow.bias += delta * 2.5;
    flashlight.shadow.bias = Math.max(-0.025, Math.min(0.025, flashlight.shadow.bias));
    console.log(`Flashlight bias adjusted: ${flashlight.shadow.bias.toFixed(6)}`);
  }
}

function adjustSmallTerrainThreshold(delta) {
  SMALL_TERRAIN_THRESHOLD = Math.max(0, SMALL_TERRAIN_THRESHOLD + delta);
  console.log(`Small terrain threshold: ${SMALL_TERRAIN_THRESHOLD}`);
  updatePerformanceDisplay();
}

function toggleSmallTerrainRemoval() {
  SMALL_TERRAIN_THRESHOLD = SMALL_TERRAIN_THRESHOLD > 0 ? 0 : 75;
  console.log(`Small terrain removal: ${SMALL_TERRAIN_THRESHOLD > 0 ? 'ON' : 'OFF'} (threshold: ${SMALL_TERRAIN_THRESHOLD})`);
  updatePerformanceDisplay();
}

// ====================================================================================
// ====================================================================================
// END OF MODULE: inputManager.js
// ====================================================================================
// ====================================================================================



// ====================================================================================
// ====================================================================================
// MODULE: flashlight.js
// PURPOSE: Encapsulates all logic for the player's flashlight, including its
// creation, state updates, battery mechanics, and visual effects like flickering.
// ====================================================================================
// ====================================================================================

let flashlight = null;
let flashlightOn = false;
let flashlightBattery = 100;

/**
 * Initializes the flashlight (a Three.js SpotLight) and adds it to the scene.
 */
function initFlashlight() {
  const flashlightRange = 25, flashlightAngle = Math.PI / 3, flashlightPenumbra = 0.8, flashlightDecay = 2.5;
  flashlight = new THREE.SpotLight(0xfff8dc, 4.0, flashlightRange, flashlightAngle, flashlightPenumbra, flashlightDecay);
  flashlight.castShadow = true;
  flashlight.shadow.mapSize.width = 1024;
  flashlight.shadow.mapSize.height = 1024;
  flashlight.shadow.camera.near = 0.1;
  flashlight.shadow.camera.far = flashlightRange;
  flashlight.shadow.camera.fov = (flashlightAngle * 180 / Math.PI);
  // Improved flashlight shadow settings to match directional light
  flashlight.shadow.bias = -0.001;  // Less aggressive bias
  flashlight.shadow.normalBias = 0.05;  // Higher normal bias for consistency
  flashlight.shadow.radius = 8;  // Softer shadows
  flashlight.target = new THREE.Object3D();
  scene.add(flashlight.target);
  flashlight.visible = false;
  scene.add(flashlight);
}

/**
 * Toggles the flashlight on or off based on its current state and battery level.
 */
function toggleFlashlight() {
  if (flashlightBattery >= flashlightMinBattery && !flashlightOn) {
    flashlightOn = true;
    flashlight.visible = true;
  } else if (flashlightOn) {
    flashlightOn = false;
    flashlight.visible = false;
  }
  updateFlashlightDisplay();
}

/**
 * Updates the flashlight's state, including battery drain/recharge and visual effects.
 * @param {number} delta - The time elapsed since the last frame.
 */
function updateFlashlight(delta) {
  if (flashlightOn) {
    flashlightBattery = Math.max(0, flashlightBattery - flashlightDecayRate * delta);
    if (flashlightBattery <= 0) {
      flashlightOn = false;
      flashlight.visible = false;
    }
    const batteryRatio = flashlightBattery / 100;
    let intensityCurve = Math.pow(batteryRatio, 0.5);
    if (flashlightBattery < 15) {
      intensityCurve *= (1.0 + (0.1 + Math.random() * 0.2) + (Math.sin(performance.now() * 0.01) * 0.1));
    }
    flashlight.intensity = 4.0 * Math.max(0.05, intensityCurve);
    if (flashlightBattery > 50) flashlight.color.setHex(0xfff8dc);
    else if (flashlightBattery > 25) flashlight.color.setHex(0xfff0b8);
    else if (flashlightBattery > 10) flashlight.color.setHex(0xffe8a0);
    else flashlight.color.setHex(0xffd080);
  } else {
    flashlightBattery = Math.min(100, flashlightBattery + flashlightRechargeRate * delta);
  }

  if (flashlight && flashlight.visible) {
    flashlight.position.copy(camera.position);
    const forward = new THREE.Vector3(0, 0, -1).applyQuaternion(camera.quaternion);
    flashlight.target.position.copy(camera.position.clone().add(forward));
    flashlight.target.updateMatrixWorld();
  }
  updateFlashlightDisplay();
}

// ====================================================================================
// ====================================================================================
// END OF MODULE: flashlight.js
// ====================================================================================
// ====================================================================================



// ====================================================================================
// ====================================================================================
// MODULE: gpuTerrain.js
// PURPOSE: Contains experimental logic for GPU-accelerated terrain generation using
// a compute shader. This can be enabled/disabled via performance settings.
// ====================================================================================
// ====================================================================================

let gpuCompute = null;
const terrainComputeShaderSource = `
  precision highp float;
  uniform float u_planetRadius;
  uniform float u_gridSize;
  uniform float u_gridExtent;
  uniform float u_voxelSize;
  uniform float u_time;

  float hash(vec3 p) { p = fract(p * 0.3183099 + 0.1); p *= 17.0; return fract(p.x * p.y * p.z * (p.x + p.y + p.z)); }
  float noise(vec3 p) { vec3 i = floor(p); vec3 f = fract(p); f = f * f * (3.0 - 2.0 * f); return mix(mix(mix(hash(i + vec3(0,0,0)), hash(i + vec3(1,0,0)), f.x), mix(hash(i + vec3(0,1,0)), hash(i + vec3(1,1,0)), f.x), f.y), mix(mix(hash(i + vec3(0,0,1)), hash(i + vec3(1,0,1)), f.x), mix(hash(i + vec3(0,1,1)), hash(i + vec3(1,1,1)), f.x), f.y), f.z); }
  float fbm(vec3 p, int octaves) { float v=0.0, a=1.0, f=1.0, mv=0.0; for (int i=0; i<8; i++) { if(i>=octaves) break; v += noise(p * f) * a; mv += a; f *= 2.0; a *= 0.5; } return (v/mv) * 2.0 - 1.0; }

  void main() {
    float voxelIndex = gl_FragCoord.y * (u_gridSize + 1.0) + gl_FragCoord.x;
    float z = floor(voxelIndex / ((u_gridSize + 1.0) * (u_gridSize + 1.0)));
    float remainder = voxelIndex - z * (u_gridSize + 1.0) * (u_gridSize + 1.0);
    float y = floor(remainder / (u_gridSize + 1.0));
    float x = remainder - y * (u_gridSize + 1.0);
    vec3 worldPos = vec3(x, y, z) * u_voxelSize - u_gridExtent;
    float dist = length(worldPos);
    float sdf = dist - u_planetRadius;
    sdf -= (fbm(worldPos * 0.05, 4) * 12.0 + fbm(worldPos * 0.1, 3) * 6.0 + fbm(worldPos * 0.2, 2) * 2.0);
    float cave1 = abs(fbm(worldPos * 0.02, 3));
    float cave2 = abs(fbm(worldPos * 0.026 + 1000.0, 3));
    if (cave1 < 0.3 && cave2 < 0.3) sdf = max(sdf, -5.0);
    if (cave1 < 0.2) sdf = max(sdf, -3.0);
    gl_FragColor = vec4(sdf, 0.0, 0.0, 1.0);
  }
`;

/**
 * Initializes the GPU compute setup if supported and enabled.
 * @returns {boolean} - True if initialization was successful, false otherwise.
 */
function initGPUCompute() {
  if (!performanceMode.useGPU || !renderer) return false;
  try {
    const gl = renderer.getContext();
    if (!gl.getExtension('OES_texture_float') && !gl.getExtension('EXT_color_buffer_float')) {
      console.warn('GPU compute requires float texture support');
      performanceMode.useGPU = false; return false;
    }
    const size = GRID_SIZE + 1;
    const renderTarget = new THREE.WebGLRenderTarget(size, size, { format: THREE.RGBAFormat, type: THREE.FloatType, minFilter: THREE.NearestFilter, magFilter: THREE.NearestFilter });
    const computeMaterial = new THREE.ShaderMaterial({ fragmentShader: terrainComputeShaderSource, uniforms: { u_planetRadius: { value: planetRadius }, u_gridSize: { value: GRID_SIZE }, u_gridExtent: { value: GRID_EXTENT }, u_voxelSize: { value: VOXEL_SIZE }, u_time: { value: 0.0 } } });
    const computeScene = new THREE.Scene();
    const computeCamera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);
    const computeMesh = new THREE.Mesh(new THREE.PlaneGeometry(2, 2), computeMaterial);
    computeScene.add(computeMesh);
    gpuCompute = { renderTarget, material: computeMaterial, scene: computeScene, camera: computeCamera };
    console.log('GPU compute initialized successfully');
    return true;
  } catch (error) {
    console.warn('GPU compute initialization failed, falling back to CPU:', error);
    performanceMode.useGPU = false; gpuCompute = null; return false;
  }
}

/**
 * Generates the entire terrain voxel data on the GPU.
 * @returns {boolean} - True if generation was successful, false otherwise.
 */
function generateTerrainGPU() {
  if (!gpuCompute || !renderer) return false;
  try {
    console.log('Generating terrain on GPU...');
    updateLoadingProgress(50, 'GPU terrain generation...');
    renderer.setRenderTarget(gpuCompute.renderTarget);
    renderer.render(gpuCompute.scene, gpuCompute.camera);
    renderer.setRenderTarget(null);

    const size = GRID_SIZE + 1;
    const buffer = new Float32Array(size * size * 4);
    renderer.readRenderTargetPixels(gpuCompute.renderTarget, 0, 0, size, size, buffer);

    for (let z=0; z<=GRID_SIZE; z++) for (let y=0; y<=GRID_SIZE; y++) for (let x=0; x<=GRID_SIZE; x++) {
      const voxelIndex3D = x + y * size + z * size * size;
      const textureY = Math.floor(voxelIndex3D / size);
      const textureX = voxelIndex3D % size;
      if (textureY < size && textureX < size) {
        voxelData[voxelIndex(x, y, z)] = buffer[(textureY * size + textureX) * 4];
      }
    }
    updateLoadingProgress(100, 'GPU terrain generation complete!');
    console.log('GPU terrain generation complete');
    return true;
  } catch (error) {
    console.error('GPU terrain generation failed:', error);
    return false;
  }
}

// ====================================================================================
// ====================================================================================
// END OF MODULE: gpuTerrain.js
// ====================================================================================
// ====================================================================================



// ====================================================================================
// ====================================================================================
// MODULE: terrainManager.js
// PURPOSE: Core module for terrain management. It handles voxel data, chunking,
// meshing via a web worker, terrain modifications (sculpting), and SDF sampling for
// collisions.
// ====================================================================================
// ====================================================================================

// --- Terrain State ---
let terrain;
let sculptGuideCircle = null;
let terrainChunks = new Map();
let chunkUpdateQueue = new Set();
let voxelData = new Float32Array((GRID_SIZE + 1) * (GRID_SIZE + 1) * (GRID_SIZE + 1));
let terrainContainer = new THREE.Object3D();
let currentSculptRadius = SCULPT_RADIUS_MID;

// --- Web Worker ---
let terrainWorker = null;
let isWorkerBusy = false;

// --- Frustum Culling ---
let frustum = new THREE.Frustum();
let cameraMatrix = new THREE.Matrix4();

/**
 * Initializes the terrain system, including voxel data, the worker, and chunk generation.
 */
function initTerrain() {
  scene.add(terrainContainer);
  if (performanceMode.useGPU) initGPUCompute();
  initVoxelData();
  initTerrainWorker();

  updateLoadingProgress(100, 'Queueing chunks for generation...');
  for (let z=0; z<CHUNKS_PER_AXIS; z++) for (let y=0; y<CHUNKS_PER_AXIS; y++) for (let x=0; x<CHUNKS_PER_AXIS; x++) {
    chunkUpdateQueue.add(getChunkKey(x, y, z));
  }
  setTimeout(() => { generateResourceNodes(); }, 2000); // Generate resources after terrain
}

/**
 * Initializes the web worker for terrain meshing.
 */
function initTerrainWorker() {
  try {
    terrainWorker = new Worker('terrain-worker.js');
    terrainWorker.onmessage = e => {
      if (e.data.type === 'chunkGenerated') {
        updateChunkMesh(e.data.data);
        isWorkerBusy = false;
      }
    };
    terrainWorker.onerror = error => {
      console.error('Terrain worker error:', error);
      isWorkerBusy = false;
    };
    window.addEventListener('beforeunload', () => { if (terrainWorker) terrainWorker.terminate(); });
  } catch (error) {
    console.error("Failed to create terrain worker. The game will not function correctly.", error);
    // Provide a user-facing error message
    alert("Error: Could not load essential game components (Terrain Worker). Please check your browser settings or try a different browser.");
  }
}

/**
 * Generates the initial voxel data for the entire planet, either on CPU or GPU.
 */
function initVoxelData() {
  if (performanceMode.useGPU && generateTerrainGPU()) return;

  console.log('Generating procedural planet terrain on CPU...');
  updateLoadingProgress(0, 'CPU terrain generation...');
  for (let z=0; z<=GRID_SIZE; z++) {
    updateLoadingProgress((z / GRID_SIZE) * 100, `CPU layer ${z + 1}/${GRID_SIZE + 1}...`);
    for (let y=0; y<=GRID_SIZE; y++) for (let x=0; x<=GRID_SIZE; x++) {
      const wx = x*VOXEL_SIZE - GRID_EXTENT, wy = y*VOXEL_SIZE - GRID_EXTENT, wz = z*VOXEL_SIZE - GRID_EXTENT;
      const dist = Math.sqrt(wx*wx + wy*wy + wz*wz);
      let sdf = dist - planetRadius;
      sdf -= (fbm3D(wx*0.05, wy*0.05, wz*0.05, 4) * 12.0 + fbm3D(wx*0.1, wy*0.1, wz*0.1, 3) * 6.0 + fbm3D(wx*0.2, wy*0.2, wz*0.2, 2) * 2.0);
      const cave1 = Math.abs(fbm3D(wx*0.02, wy*0.02, wz*0.02, 3));
      const cave2 = Math.abs(fbm3D(wx*0.026+1000, wy*0.026, wz*0.026, 3));
      if (cave1 < 0.3 && cave2 < 0.3) sdf = Math.max(sdf, -5.0);
      if (cave1 < 0.2) sdf = Math.max(sdf, -3.0);
      voxelData[voxelIndex(x,y,z)] = sdf;
    }
  }
  updateLoadingProgress(100, 'Terrain generation complete!');
}

function voxelIndex(x, y, z) { return x + y * (GRID_SIZE + 1) + z * (GRID_SIZE + 1) * (GRID_SIZE + 1); }
function fbm3D(x, y, z, octaves=4) { let v=0, a=1, f=1, mv=0; for (let i=0; i<octaves; i++) { v += noise3D(x*f, y*f, z*f) * a; mv += a; f *= 2; a *= 0.5; } return (v/mv)*2 - 1; }
function noise3D(x, y, z) { const ix=Math.floor(x), iy=Math.floor(y), iz=Math.floor(z), fx=x-ix, fy=y-iy, fz=z-iz, ux=fx*fx*(3-2*fx), uy=fy*fy*(3-2*fy), uz=fz*fz*(3-2*fz); const n000=hash(ix,iy,iz), n100=hash(ix+1,iy,iz), n010=hash(ix,iy+1,iz), n110=hash(ix+1,iy+1,iz), n001=hash(ix,iy,iz+1), n101=hash(ix+1,iy,iz+1), n011=hash(ix,iy+1,iz+1), n111=hash(ix+1,iy+1,iz+1); const nx00=n000*(1-ux)+n100*ux, nx01=n001*(1-ux)+n101*ux, nx10=n010*(1-ux)+n110*ux, nx11=n011*(1-ux)+n111*ux, nxy0=nx00*(1-uy)+nx10*uy, nxy1=nx01*(1-uy)+nx11*uy; return nxy0*(1-uz)+nxy1*uz; }
function hash(x,y,z) { let h = Math.sin(x*12.9898 + y*78.233 + z*37.719) * 43758.5453; return h - Math.floor(h); }

/**
 * Processes the next chunk in the update queue, if the worker is available.
 */
function processChunkUpdateQueue() {
  if (isWorkerBusy || chunkUpdateQueue.size === 0) return;
  const chunkKey = chunkUpdateQueue.values().next().value;
  chunkUpdateQueue.delete(chunkKey);
  const [chunkX, chunkY, chunkZ] = chunkKey.split(',').map(Number);
  generateChunkMesh(chunkX, chunkY, chunkZ);
  
  const statusElement = document.getElementById('terrain-status');
  if (statusElement) {
      const totalChunks = CHUNKS_PER_AXIS ** 3;
      const progress = Math.floor(((totalChunks - chunkUpdateQueue.size) / totalChunks) * 100);
      statusElement.textContent = progress.toString().padStart(3, '0');
      statusElement.className = 'perf-warning';
  }
}

function getChunkKey(chunkX, chunkY, chunkZ) { return `${chunkX},${chunkY},${chunkZ}`; }

/**
 * Sends a chunk's data to the web worker for mesh generation.
 * @param {number} chunkX - The X coordinate of the chunk.
 * @param {number} chunkY - The Y coordinate of the chunk.
 * @param {number} chunkZ - The Z coordinate of the chunk.
 */
function generateChunkMesh(chunkX, chunkY, chunkZ) {
    isWorkerBusy = true;
    const startX=chunkX*CHUNK_SIZE, startY=chunkY*CHUNK_SIZE, startZ=chunkZ*CHUNK_SIZE;
    const chunkSizeWithSkirt = CHUNK_SIZE + 2;
    const chunkVoxelData = new Float32Array(chunkSizeWithSkirt**3);
    for (let z=0; z<chunkSizeWithSkirt; z++) for (let y=0; y<chunkSizeWithSkirt; y++) for (let x=0; x<chunkSizeWithSkirt; x++) {
        const gx = Math.max(0, Math.min(startX + x - 1, GRID_SIZE));
        const gy = Math.max(0, Math.min(startY + y - 1, GRID_SIZE));
        const gz = Math.max(0, Math.min(startZ + z - 1, GRID_SIZE));
        chunkVoxelData[x + y*chunkSizeWithSkirt + z*chunkSizeWithSkirt*chunkSizeWithSkirt] = voxelData[voxelIndex(gx, gy, gz)] || 1.0;
    }
    terrainWorker.postMessage({ type: 'generateChunk', data: { voxelData: chunkVoxelData, chunkX, chunkY, chunkZ, chunkSize: CHUNK_SIZE, worldOffset: { x: startX, y: startY, z: startZ }, gridExtent: GRID_EXTENT, voxelSize: VOXEL_SIZE, isoLevel: ISO_LEVEL, globalVoxelData: voxelData, globalGridSize: GRID_SIZE, smallTerrainThreshold: SMALL_TERRAIN_THRESHOLD } });
}

/**
 * Receives generated mesh data from the worker and updates the corresponding chunk in the scene.
 * @param {object} data - The data received from the worker, including mesh data and chunk coordinates.
 */
function updateChunkMesh(data) {
  const { meshData, chunkX, chunkY, chunkZ } = data;
  const { positions, normals, colors } = meshData;
  const chunkKey = getChunkKey(chunkX, chunkY, chunkZ);
  let chunkMesh = terrainChunks.get(chunkKey);

  if (positions.length === 0) {
    if (chunkMesh) {
      terrainContainer.remove(chunkMesh);
      chunkMesh.geometry.dispose(); chunkMesh.material.dispose();
      terrainChunks.delete(chunkKey);
    }
    if (polygonalMeshEnabled) polygonalMeshNeedsUpdate = true;
    return;
  }

  const geometry = new THREE.BufferGeometry();
  geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
  geometry.setAttribute('normal', new THREE.BufferAttribute(normals, 3));
  geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
  geometry.computeBoundingSphere();

  if (chunkMesh) {
    chunkMesh.geometry.dispose();
    chunkMesh.geometry = geometry;
  } else {
    chunkMesh = new THREE.Mesh(geometry, createTerrainMaterial());
    chunkMesh.castShadow = true; chunkMesh.receiveShadow = true;
    chunkMesh.frustumCulled = true; chunkMesh.name = `chunk_${chunkKey}`;
    terrainContainer.add(chunkMesh);
    terrainChunks.set(chunkKey, chunkMesh);
  }
  if (polygonalMeshEnabled) polygonalMeshNeedsUpdate = true;
}

/**
 * Updates the visibility of terrain chunks based on camera frustum culling.
 */
function updateFrustumCulling() {
    updateFrustum(camera);
    terrainContainer.children.forEach(chunk => {
        const [cx, cy, cz] = chunk.name.replace('chunk_', '').split(',').map(Number);
        chunk.visible = isChunkInFrustum(cx, cy, cz, camera);
    });
}

function updateFrustum(camera) {
  cameraMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
  frustum.setFromProjectionMatrix(cameraMatrix);
}

function isChunkInFrustum(chunkX, chunkY, chunkZ, camera) {
    const minX = chunkX * CHUNK_SIZE * VOXEL_SIZE - GRID_EXTENT;
    const minY = chunkY * CHUNK_SIZE * VOXEL_SIZE - GRID_EXTENT;
    const minZ = chunkZ * CHUNK_SIZE * VOXEL_SIZE - GRID_EXTENT;
    const chunkCenter = new THREE.Vector3(minX, minY, minZ).addScalar(CHUNK_SIZE * VOXEL_SIZE / 2);
    if (camera.position.distanceTo(chunkCenter) > performanceMode.farClip * performanceMode.cullingDistance) return false;
    const chunkBox = new THREE.Box3(new THREE.Vector3(minX, minY, minZ), new THREE.Vector3(minX + CHUNK_SIZE*VOXEL_SIZE, minY + CHUNK_SIZE*VOXEL_SIZE, minZ + CHUNK_SIZE*VOXEL_SIZE));
    return frustum.intersectsBox(chunkBox);
}

/**
 * Creates a new material for terrain chunks based on current performance settings.
 * @returns {THREE.MeshStandardMaterial} The newly created material.
 */
function createTerrainMaterial() {
  return new THREE.MeshStandardMaterial({ vertexColors: performanceMode.terrainColors, color: performanceMode.terrainColors ? 0xffffff : 0x4a7c59, flatShading: true, side: performanceMode.backfaceCulling ? THREE.FrontSide : THREE.DoubleSide, roughness: 0.8, metalness: 0.1, shadowSide: THREE.DoubleSide });
}

/**
 * Updates the material of all terrain chunks to reflect changes in performance settings.
 */
function updateTerrainMaterial() {
  terrainContainer.children.forEach(chunkMesh => {
    if (chunkMesh && chunkMesh.material) {
      chunkMesh.material.side = performanceMode.backfaceCulling ? THREE.FrontSide : THREE.DoubleSide;
      chunkMesh.material.vertexColors = performanceMode.terrainColors;
      chunkMesh.material.color.setHex(performanceMode.terrainColors ? 0xffffff : 0x4a7c59);
      chunkMesh.material.needsUpdate = true;
    }
  });
}

/**
 * Handles the continuous sculpting action when the mouse button is held down.
 */
function handleContinuousSculpting() {
    const currentTime = performance.now();
    if (mouseButtons.left || mouseButtons.right) {
        const holdDuration = currentTime - sculptStartTime;
        const speedFactor = Math.min(1.0, holdDuration / sculptAcceleration);
        const currentInterval = sculptInterval - (sculptInterval - minSculptInterval) * speedFactor;
        if (currentTime - lastSculptTime >= currentInterval) {
            performSculpting();
            lastSculptTime = currentTime;
        }
    }
}

/**
 * Performs a single terrain sculpting operation at the cursor's location.
 */
function performSculpting() {
  raycaster.setFromCamera({ x: 0, y: 0 }, camera);
  const inter = raycaster.intersectObject(terrainContainer, true);
  if (inter.length > 0) {
    const isAdding = mouseButtons.left;
    modifyTerrain(inter[0].point, isAdding);
    createMiningParticles(inter[0].point, isAdding, Math.floor(currentSculptRadius * 3));
  }
}

/**
 * Modifies the voxel data based on a sculpting action.
 * @param {THREE.Vector3} point - The world-space point of modification.
 * @param {boolean} add - True to add terrain, false to remove.
 */
function modifyTerrain(point, add) {
  terrainUpdateCount++;
  if (!add) {
    const resourceHit = checkResourceInteraction(point, true);
    if (resourceHit) {
      mineResource(resourceHit, point);
      return;
    }
  }

  const gx = (point.x + GRID_EXTENT) / VOXEL_SIZE, gy = (point.y + GRID_EXTENT) / VOXEL_SIZE, gz = (point.z + GRID_EXTENT) / VOXEL_SIZE;
  const radVox = Math.ceil(currentSculptRadius / VOXEL_SIZE);
  const minX=Math.max(0,Math.floor(gx-radVox)), maxX=Math.min(GRID_SIZE,Math.floor(gx+radVox));
  const minY=Math.max(0,Math.floor(gy-radVox)), maxY=Math.min(GRID_SIZE,Math.floor(gy+radVox));
  const minZ=Math.max(0,Math.floor(gz-radVox)), maxZ=Math.min(GRID_SIZE,Math.floor(gz+radVox));
  const affectedChunks = new Set();

  for (let z=minZ; z<=maxZ; z++) for (let y=minY; y<=maxY; y++) for (let x=minX; x<=maxX; x++) {
    const wx = x*VOXEL_SIZE-GRID_EXTENT, wy = y*VOXEL_SIZE-GRID_EXTENT, wz = z*VOXEL_SIZE-GRID_EXTENT;
    const dist = Math.sqrt((wx-point.x)**2 + (wy-point.y)**2 + (wz-point.z)**2);
    if (dist <= currentSculptRadius) {
      voxelData[voxelIndex(x,y,z)] += (add ? -1 : 1) * (1 - (dist / currentSculptRadius)) * TERRAIN_MODIFICATION_STRENGTH;
      if (polygonalMeshEnabled) polygonalMeshNeedsUpdate = true;
      const chunkX=Math.floor(x/CHUNK_SIZE), chunkY=Math.floor(y/CHUNK_SIZE), chunkZ=Math.floor(z/CHUNK_SIZE);
      affectedChunks.add(getChunkKey(chunkX,chunkY,chunkZ));
      if (x%CHUNK_SIZE===0 && chunkX>0) affectedChunks.add(getChunkKey(chunkX-1,chunkY,chunkZ));
      if (x%CHUNK_SIZE===CHUNK_SIZE-1 && chunkX<CHUNKS_PER_AXIS-1) affectedChunks.add(getChunkKey(chunkX+1,chunkY,chunkZ));
      if (y%CHUNK_SIZE===0 && chunkY>0) affectedChunks.add(getChunkKey(chunkX,chunkY-1,chunkZ));
      if (y%CHUNK_SIZE===CHUNK_SIZE-1 && chunkY<CHUNKS_PER_AXIS-1) affectedChunks.add(getChunkKey(chunkX,chunkY+1,chunkZ));
      if (z%CHUNK_SIZE===0 && chunkZ>0) affectedChunks.add(getChunkKey(chunkX,chunkY,chunkZ-1));
      if (z%CHUNK_SIZE===CHUNK_SIZE-1 && chunkZ<CHUNKS_PER_AXIS-1) affectedChunks.add(getChunkKey(chunkX,chunkY,chunkZ+1));
    }
  }
  affectedChunks.forEach(key => chunkUpdateQueue.add(key));
  createTerrainSculptSmoke(point, add);
}

function createTerrainSculptSmoke(position, isAdding) {
  for (let i = 0; i < (isAdding ? 3 : 5); i++) {
    resourceSmokeParticles.push(new SmokeParticle(position.clone(), 0x888888, 0.8 + Math.random() * 1.2));
  }
}

/**
 * Updates the visual guide for the sculpting tool.
 * @param {THREE.Vector3} position - The position to place the guide.
 * @param {THREE.Vector3} normal - The surface normal to align the guide to.
 */
function updateSculptGuide(position, normal) {
  if (sculptGuideCircle) scene.remove(sculptGuideCircle);
  const circleGeo = new THREE.RingGeometry(currentSculptRadius*0.95, currentSculptRadius, 32);
  const circleMat = new THREE.MeshBasicMaterial({ color: 0xffffff, transparent: true, opacity: 0.4, side: THREE.DoubleSide, depthTest: false, depthWrite: false });
  sculptGuideCircle = new THREE.Mesh(circleGeo, circleMat);
  sculptGuideCircle.position.copy(position);
  sculptGuideCircle.lookAt(position.clone().add(normal));
  sculptGuideCircle.renderOrder = 999;
  scene.add(sculptGuideCircle);
}

function hideSculptGuide() {
  if (sculptGuideCircle) {
    scene.remove(sculptGuideCircle);
    sculptGuideCircle.geometry.dispose();
    sculptGuideCircle.material.dispose();
    sculptGuideCircle = null;
  }
}

/**
 * Samples the Signed Distance Function (SDF) at a given world position using trilinear interpolation.
 * @param {THREE.Vector3} pos - The world-space position to sample.
 * @returns {number} The SDF value at that position.
 */
function sampleSDF(pos) {
    const fx=(pos.x+GRID_EXTENT)/VOXEL_SIZE, fy=(pos.y+GRID_EXTENT)/VOXEL_SIZE, fz=(pos.z+GRID_EXTENT)/VOXEL_SIZE;
    const x0=Math.floor(fx), y0=Math.floor(fy), z0=Math.floor(fz);
    const x1=x0+1, y1=y0+1, z1=z0+1;
    const tx=fx-x0, ty=fy-y0, tz=fz-z0;
    const clamp=v=>Math.min(Math.max(v,0),GRID_SIZE);
    const v000=voxelData[voxelIndex(clamp(x0),clamp(y0),clamp(z0))], v100=voxelData[voxelIndex(clamp(x1),clamp(y0),clamp(z0))];
    const v010=voxelData[voxelIndex(clamp(x0),clamp(y1),clamp(z0))], v110=voxelData[voxelIndex(clamp(x1),clamp(y1),clamp(z0))];
    const v001=voxelData[voxelIndex(clamp(x0),clamp(y0),clamp(z1))], v101=voxelData[voxelIndex(clamp(x1),clamp(y0),clamp(z1))];
    const v011=voxelData[voxelIndex(clamp(x0),clamp(y1),clamp(z1))], v111=voxelData[voxelIndex(clamp(x1),clamp(y1),clamp(z1))];
    const v00=v000*(1-tx)+v100*tx, v10=v010*(1-tx)+v110*tx, v01=v001*(1-tx)+v101*tx, v11=v011*(1-tx)+v111*tx;
    const v0=v00*(1-ty)+v10*ty, v1=v01*(1-ty)+v11*ty;
    return v0*(1-tz)+v1*tz;
}

/**
 * Calculates the surface normal at a given world position by sampling the SDF gradient.
 * @param {THREE.Vector3} pos - The world-space position.
 * @returns {THREE.Vector3} The normalized surface normal.
 */
function calculateSurfaceNormal(pos) {
    const e=0.1;
    const dx = sampleSDF(new THREE.Vector3(pos.x+e, pos.y, pos.z)) - sampleSDF(new THREE.Vector3(pos.x-e, pos.y, pos.z));
    const dy = sampleSDF(new THREE.Vector3(pos.x, pos.y+e, pos.z)) - sampleSDF(new THREE.Vector3(pos.x, pos.y-e, pos.z));
    const dz = sampleSDF(new THREE.Vector3(pos.x, pos.y, pos.z+e)) - sampleSDF(new THREE.Vector3(pos.x, pos.y, pos.z-e));
    return new THREE.Vector3(dx,dy,dz).normalize();
}

// ====================================================================================
// ====================================================================================
// END OF MODULE: terrainManager.js
// ====================================================================================
// ====================================================================================



// ====================================================================================
// ====================================================================================
// MODULE: polygonalMeshVisualizer.js
// PURPOSE: Creates a dynamic, transparent polygonal mesh overlay that visualizes the
// marching cubes structure near the player for debugging and enhanced physics.
// ====================================================================================
// ====================================================================================

function initPolygonalMeshContainer() {
  if (!polygonalMeshContainer) {
    polygonalMeshContainer = new THREE.Object3D();
    polygonalMeshContainer.name = 'polygonalMeshContainer';
    scene.add(polygonalMeshContainer);
  }
}

function clearPolygonalMesh() {
  if (polygonalMeshContainer) {
    polygonalMeshContainer.children.forEach(child => {
      if (child.geometry) child.geometry.dispose();
      if (child.material) child.material.dispose();
    });
    polygonalMeshContainer.clear();
    scene.remove(polygonalMeshContainer);
    polygonalMeshContainer = null;
  }
  polygonalPhysicsData = null;
}

function updatePolygonalMeshIfNeeded() {
  const distance = camera.position.distanceTo(lastPolygonalMeshPosition);
  const shouldUpdate = distance > polygonalMeshUpdateThreshold || (polygonalMeshNeedsUpdate && performance.now() - lastPolygonalMeshUpdate > polygonalMeshUpdateCooldown);
  if (shouldUpdate) {
    updatePolygonalMesh();
    lastPolygonalMeshPosition.copy(camera.position);
    polygonalMeshNeedsUpdate = false;
    lastPolygonalMeshUpdate = performance.now();
  }
}

function updatePolygonalMesh() {
  if (!polygonalMeshContainer) return;
  polygonalMeshContainer.children.forEach(child => {
    if (child.geometry) child.geometry.dispose();
    if (child.material) child.material.dispose();
  });
  polygonalMeshContainer.clear();
  generatePolygonalMeshAroundPlayer();
}

function generatePolygonalMeshAroundPlayer() {
  const marchingCubesVertices = extractMarchingCubesVertices(camera.position, polygonalMeshRadius);
  if (marchingCubesVertices.length === 0) return;

  const polygonalVertices = [], vertexMap = new Map();
  marchingCubesVertices.forEach((mcVertex, index) => {
    polygonalVertices.push(mcVertex.clone().add(mcVertex.normal.clone().multiplyScalar(0.08)));
    vertexMap.set(`${mcVertex.x.toFixed(3)},${mcVertex.y.toFixed(3)},${mcVertex.z.toFixed(3)}`, index);
  });

  const polygonalPositions = [], polygonalIndices = [];
  polygonalVertices.forEach(v => polygonalPositions.push(v.x, v.y, v.z));
  extractTriangleConnectivity(camera.position, polygonalMeshRadius, vertexMap).forEach(t => polygonalIndices.push(t.i1, t.i2, t.i3));

  const geometry = new THREE.BufferGeometry();
  geometry.setAttribute('position', new THREE.BufferAttribute(new Float32Array(polygonalPositions), 3));
  geometry.setIndex(polygonalIndices);
  geometry.computeVertexNormals();
  const subdividedGeometry = subdivideGeometry(geometry, 1);

  const material = new THREE.MeshBasicMaterial({ color: 0x00ff00, wireframe: true, transparent: true, opacity: 0.6, depthTest: true, depthWrite: false });
  const mesh = new THREE.Mesh(subdividedGeometry, material);
  polygonalMeshContainer.add(mesh);

  if (usePolygonalPhysics) polygonalPhysicsData = extractPhysicsDataFromGeometry(subdividedGeometry, camera.position);
}

function subdivideGeometry(geometry, levels) {
  let currentGeometry = geometry.clone();
  for (let level = 0; level < levels; level++) currentGeometry = subdivideGeometryOnce(currentGeometry);
  return currentGeometry;
}

function subdivideGeometryOnce(geometry) {
  const positions = geometry.attributes.position.array, indices = geometry.index.array;
  const newPositions = Array.from(positions), newIndices = [], edgeMap = new Map();

  const getMidpointVertex = (i1, i2) => {
    const key = i1 < i2 ? `${i1}-${i2}` : `${i2}-${i1}`;
    if (edgeMap.has(key)) return edgeMap.get(key);
    const midX = (positions[i1*3] + positions[i2*3]) * 0.5, midY = (positions[i1*3+1] + positions[i2*3+1]) * 0.5, midZ = (positions[i1*3+2] + positions[i2*3+2]) * 0.5;
    const newIndex = newPositions.length / 3;
    newPositions.push(midX, midY, midZ);
    edgeMap.set(key, newIndex);
    return newIndex;
  };

  for (let i = 0; i < indices.length; i += 3) {
    const i1 = indices[i], i2 = indices[i+1], i3 = indices[i+2];
    const m12 = getMidpointVertex(i1, i2), m23 = getMidpointVertex(i2, i3), m31 = getMidpointVertex(i3, i1);
    newIndices.push(i1, m12, m31, m12, i2, m23, m31, m23, i3, m12, m23, m31);
  }

  const newGeometry = new THREE.BufferGeometry();
  newGeometry.setAttribute('position', new THREE.BufferAttribute(new Float32Array(newPositions), 3));
  newGeometry.setIndex(newIndices);
  newGeometry.computeVertexNormals();
  return newGeometry;
}

function extractPhysicsDataFromGeometry(geometry, centerPos) {
  const positions = geometry.attributes.position.array, indices = geometry.index.array, normals = geometry.attributes.normal.array;
  const triangles = [], spatialGrid = new Map(), gridSize = 0.3;

  for (let i = 0; i < indices.length; i += 3) {
    const i1=indices[i]*3, i2=indices[i+1]*3, i3=indices[i+2]*3;
    const v1 = new THREE.Vector3(positions[i1], positions[i1+1], positions[i1+2]);
    const v2 = new THREE.Vector3(positions[i2], positions[i2+1], positions[i2+2]);
    const v3 = new THREE.Vector3(positions[i3], positions[i3+1], positions[i3+2]);
    const center = new THREE.Vector3().addVectors(v1,v2).add(v3).divideScalar(3);
    const normal = new THREE.Vector3(normals[i1], normals[i1+1], normals[i1+2]).add(new THREE.Vector3(normals[i2], normals[i2+1], normals[i2+2])).add(new THREE.Vector3(normals[i3], normals[i3+1], normals[i3+2])).normalize();
    const triangle = { vertices: [v1, v2, v3], center, normal, edge1: new THREE.Vector3().subVectors(v2,v1), edge2: new THREE.Vector3().subVectors(v3,v1) };
    triangles.push(triangle);
    const gridKey = `${Math.floor(center.x/gridSize)},${Math.floor(center.y/gridSize)},${Math.floor(center.z/gridSize)}`;
    if (!spatialGrid.has(gridKey)) spatialGrid.set(gridKey, []);
    spatialGrid.get(gridKey).push(triangle);
  }
  return { triangles, spatialGrid, gridSize, centerPos: centerPos.clone() };
}

function extractMarchingCubesVertices(playerPos, radius) {
  const vertices = [], vertexSet = new Set(), radiusSq = radius*radius;
  terrainContainer.children.forEach(chunkMesh => {
    if (!chunkMesh.geometry) return;
    const positions = chunkMesh.geometry.attributes.position.array, normals = chunkMesh.geometry.attributes.normal.array;
    for (let i = 0; i < positions.length / 3; i++) {
      const vertex = new THREE.Vector3(positions[i*3], positions[i*3+1], positions[i*3+2]);
      if (vertex.distanceToSquared(playerPos) <= radiusSq) {
        const key = `${vertex.x.toFixed(3)},${vertex.y.toFixed(3)},${vertex.z.toFixed(3)}`;
        if (!vertexSet.has(key)) {
          vertexSet.add(key);
          vertex.normal = new THREE.Vector3(normals[i*3], normals[i*3+1], normals[i*3+2]);
          vertices.push(vertex);
        }
      }
    }
  });
  return vertices;
}

function extractTriangleConnectivity(playerPos, radius, vertexMap) {
  const triangles = [], radiusSq = radius*radius;
  terrainContainer.children.forEach(chunkMesh => {
    if (!chunkMesh.geometry) return;
    const positions = chunkMesh.geometry.attributes.position.array;
    for (let i = 0; i < positions.length / 9; i++) {
      const v1 = new THREE.Vector3(positions[i*9], positions[i*9+1], positions[i*9+2]);
      const v2 = new THREE.Vector3(positions[i*9+3], positions[i*9+4], positions[i*9+5]);
      const v3 = new THREE.Vector3(positions[i*9+6], positions[i*9+7], positions[i*9+8]);
      if (new THREE.Vector3().addVectors(v1,v2).add(v3).divideScalar(3).distanceToSquared(playerPos) <= radiusSq) {
        const i1 = vertexMap.get(`${v1.x.toFixed(3)},${v1.y.toFixed(3)},${v1.z.toFixed(3)}`);
        const i2 = vertexMap.get(`${v2.x.toFixed(3)},${v2.y.toFixed(3)},${v2.z.toFixed(3)}`);
        const i3 = vertexMap.get(`${v3.x.toFixed(3)},${v3.y.toFixed(3)},${v3.z.toFixed(3)}`);
        if (i1 !== undefined && i2 !== undefined && i3 !== undefined) triangles.push({ i1, i2, i3 });
      }
    }
  });
  return triangles;
}

function sampleSDFAtPosition(position) {
  const vx = (position.x + GRID_EXTENT) / VOXEL_SIZE, vy = (position.y + GRID_EXTENT) / VOXEL_SIZE, vz = (position.z + GRID_EXTENT) / VOXEL_SIZE;
  if (vx<0||vx>=GRID_SIZE||vy<0||vy>=GRID_SIZE||vz<0||vz>=GRID_SIZE) return 10.0;
  const x0=Math.floor(vx), y0=Math.floor(vy), z0=Math.floor(vz), x1=Math.min(x0+1,GRID_SIZE), y1=Math.min(y0+1,GRID_SIZE), z1=Math.min(z0+1,GRID_SIZE);
  const tx=vx-x0, ty=vy-y0, tz=vz-z0, stride=GRID_SIZE+1;
  const vidx = (x,y,z) => x + y*stride + z*stride*stride;
  const v000=voxelData[vidx(x0,y0,z0)]||10, v100=voxelData[vidx(x1,y0,z0)]||10, v010=voxelData[vidx(x0,y1,z0)]||10, v110=voxelData[vidx(x1,y1,z0)]||10;
  const v001=voxelData[vidx(x0,y0,z1)]||10, v101=voxelData[vidx(x1,y0,z1)]||10, v011=voxelData[vidx(x0,y1,z1)]||10, v111=voxelData[vidx(x1,y1,z1)]||10;
  const v00=v000*(1-tx)+v100*tx, v01=v001*(1-tx)+v101*tx, v10=v010*(1-tx)+v110*tx, v11=v011*(1-tx)+v111*tx;
  const v0=v00*(1-ty)+v10*ty, v1=v01*(1-ty)+v11*ty;
  return v0*(1-tz)+v1*tz;
}

// ====================================================================================
// ====================================================================================
// END OF MODULE: polygonalMeshVisualizer.js
// ====================================================================================
// ====================================================================================



// ====================================================================================
// ====================================================================================
// MODULE: resourceMiningSystem.js
// PURPOSE: Manages the entire resource lifecycle: generation, mining, chunk spawning,
// collection, and UI updates. Includes the resource radar/X-ray vision system.
// ====================================================================================
// ====================================================================================

function generateResourceNodes() {
  console.log('Generating resource nodes...');
  resourceNodes.clear();
  resourceContainer.clear();
  const counts = { COAL: 150, STONE: 120, GOLD: 40, EMERALD: 25 };
  Object.keys(counts).forEach(type => {
    for (let i = 0; i < counts[type]; i++) {
      let pos, attempts = 0;
      do {
        const r = planetRadius * 0.95 * Math.pow(Math.random(), 1/3);
        pos = new THREE.Vector3( (Math.random()-0.5)*2, (Math.random()-0.5)*2, (Math.random()-0.5)*2 ).normalize().multiplyScalar(r);
        attempts++;
      } while (pos.length() > planetRadius * 0.9 && attempts < 50);
      if (attempts < 50 && sampleSDFAtPosition(pos) < 0) {
        const key = `${Math.floor(pos.x)},${Math.floor(pos.y)},${Math.floor(pos.z)}`;
        if (!resourceNodes.has(key)) {
          const size = ['SMALL', 'MEDIUM', 'LARGE'][Math.floor(Math.random()*3)];
          const node = createResourceNode(type, pos, size);
          resourceNodes.set(key, { type, position: pos.clone(), mesh: node, size, active: true });
          resourceContainer.add(node);
        }
      }
    }
  });
  console.log(`Generated ${resourceNodes.size} resource nodes`);
}

function createResourceNode(type, pos, sizeType = 'MEDIUM') {
  const resData = RESOURCE_TYPES[type], sizeData = RESOURCE_SIZES[sizeType];
  const geo = new THREE.SphereGeometry(VOXEL_SIZE * 1.5 * sizeData.scale, 16, 12);
  const verts = geo.attributes.position.array;
  for (let i=0; i<verts.length; i+=3) { const n=(Math.random()-0.5)*0.15; verts[i]*=(1+n); verts[i+1]*=(1+n); verts[i+2]*=(1+n); }
  geo.attributes.position.needsUpdate = true;
  geo.computeVertexNormals();
  const mat = new THREE.MeshStandardMaterial({ color: resData.variations[Math.floor(Math.random()*3)], metalness: resData.metallic, roughness: resData.roughness });
  const mesh = new THREE.Mesh(geo, mat);
  mesh.position.copy(pos);
  mesh.userData = { resourceType: type, isResource: true, health: sizeData.health, size: sizeType, maxHealth: sizeData.health };
  mesh.rotation.set(Math.random()*Math.PI, Math.random()*Math.PI, Math.random()*Math.PI);
  return mesh;
}

function checkResourceInteraction(point, isRightClick) {
  if (!isRightClick) return null;
  let closest = null, closestDist = Infinity;
  resourceNodes.forEach(res => {
    if (res.active) {
      const dist = point.distanceTo(res.position);
      if (dist < currentSculptRadius && dist < closestDist) {
        closestDist = dist;
        closest = res;
      }
    }
  });
  return closest;
}

function mineResource(resData, sculptPoint) {
  if (!resData || !resData.active) return;
  resData.mesh.userData.health -= 25;
  createMiningSmoke(sculptPoint, resData.type);
  if (resData.mesh.userData.health <= 0) {
    spawnResourceChunks(resData);
    resourceContainer.remove(resData.mesh);
    resData.mesh.geometry.dispose();
    resData.mesh.material.dispose();
    resData.active = false;
  } else {
    const scale = 0.6 + (resData.mesh.userData.health / resData.mesh.userData.maxHealth) * 0.4;
    resData.mesh.scale.setScalar(scale);
  }
}

function createMiningSmoke(position, resourceType) {
  for (let i = 0; i < 12; i++) {
    resourceSmokeParticles.push(new ResourceGeoParticle(position.clone(), RESOURCE_TYPES[resourceType].color, resourceType, 1.5 + Math.random() * 2.0));
  }
}

function spawnResourceChunks(resData) {
  const sizeData = RESOURCE_SIZES[resData.size || 'MEDIUM'];
  const count = sizeData.chunkCount[0] + Math.floor(Math.random() * (sizeData.chunkCount[1] - sizeData.chunkCount[0] + 1));
  for (let i = 0; i < count; i++) {
    const chunk = createResourceChunk(resData.type, resData.position, resData.size);
    resourceChunks.push(chunk);
    chunkContainer.add(chunk.mesh);
  }
}

function createResourceChunk(type, pos, sizeType = 'MEDIUM') {
  const resData = RESOURCE_TYPES[type], sizeData = RESOURCE_SIZES[sizeType];
  const size = VOXEL_SIZE * 0.8 * sizeData.scale * 0.7;
  const geo = new THREE.BoxGeometry(size, size, size);
  const mat = new THREE.MeshStandardMaterial({ color: resData.color, metalness: resData.metallic, roughness: resData.roughness });
  const mesh = new THREE.Mesh(geo, mat);
  mesh.position.copy(pos).add(new THREE.Vector3((Math.random()-0.5)*2, (Math.random()-0.5)*2, (Math.random()-0.5)*2));
  const chunk = { mesh, velocity: new THREE.Vector3((Math.random()-0.5)*2, Math.random()*2+1, (Math.random()-0.5)*2), resourceType: type, lifetime: 30.0, bounces: 0, collected: false };
  mesh.userData = { isResourceChunk: true, resourceType: type, chunkData: chunk };
  if (resourceRadarEnabled) {
    const xrayMat = new THREE.MeshBasicMaterial({ color: resData.color, transparent: true, opacity: 0.9, depthTest: false, depthWrite: false, side: THREE.DoubleSide });
    resourceRadarMaterials.set(mesh.uuid, mat);
    mesh.material = xrayMat;
    mesh.renderOrder = 1000;
  }
  return chunk;
}

function updateResourceSystem(deltaTime) {
  for (let i = resourceSmokeParticles.length - 1; i >= 0; i--) if (!resourceSmokeParticles[i].update(deltaTime)) resourceSmokeParticles.splice(i, 1);
  for (let i = collectionParticles.length - 1; i >= 0; i--) if (!collectionParticles[i].update(deltaTime)) collectionParticles.splice(i, 1);
  for (let i = resourceChunks.length - 1; i >= 0; i--) {
    const chunk = resourceChunks[i];
    if (chunk.collected) { resourceChunks.splice(i, 1); continue; }
    updateResourceChunk(chunk, deltaTime);
    if (chunk.mesh.position.distanceTo(camera.position) < 3.0 || (chunk.lifetime -= deltaTime) <= 0) {
      collectResourceChunk(chunk);
      resourceChunks.splice(i, 1);
    }
  }
  updateResourceUI();
}

function updateResourceChunk(chunk, deltaTime) {
  const chunkPos = chunk.mesh.position.clone();
  const distToCenter = Math.max(chunkPos.length(), 0.1);
  // Use adaptive gravity for resource chunks
  const adaptiveGravity = calculateAdaptiveGravity(chunkPos);
  chunk.velocity.add(chunkPos.clone().divideScalar(-distToCenter).multiplyScalar(adaptiveGravity * deltaTime * 0.3));
  chunk.velocity.y += Math.sin(performance.now()*0.001 + chunk.mesh.position.x + chunk.mesh.position.z) * 0.5 * deltaTime;
  chunk.velocity.multiplyScalar(0.95);
  chunk.mesh.position.add(chunk.velocity.clone().multiplyScalar(deltaTime));
  const sdf = sampleSDFAtPosition(chunk.mesh.position);
  if (sdf < 1.0) {
    const normal = calculateSDFGradient(chunk.mesh.position);
    chunk.velocity.add(normal.clone().multiplyScalar((1.0 - sdf) * 2.0 * deltaTime));
    chunk.mesh.position.add(normal.clone().multiplyScalar((1.0 - sdf) * 0.1));
    chunk.velocity.x *= 0.8; chunk.velocity.z *= 0.8;
    if (++chunk.bounces > 1) chunk.velocity.multiplyScalar(0.7);
  }
  chunk.mesh.rotation.x += deltaTime; chunk.mesh.rotation.y += deltaTime * 0.8;
}

function collectResourceChunk(chunk) {
  resourceInventory[RESOURCE_TYPES[chunk.resourceType].name]++;
  create2DCollectionEffect(chunk.mesh.position, chunk.resourceType);
  createCollectionEffect(chunk.mesh.position, chunk.resourceType);
  chunkContainer.remove(chunk.mesh);
  chunk.mesh.geometry.dispose();
  chunk.mesh.material.dispose();
  chunk.collected = true;
}

function create2DCollectionEffect(position, resourceType) {
  for (let i = 0; i < 5 + Math.floor(Math.random() * 4); i++) {
    setTimeout(() => {
      collectionParticles.push(new CollectionParticle2D(position.clone().add(new THREE.Vector3((Math.random()-0.5)*2, (Math.random()-0.5)*2, (Math.random()-0.5)*2)), resourceType));
    }, i * 80 + Math.random() * 40);
  }
}

function createCollectionEffect(position, resourceType) {
  for (let i = 0; i < 3; i++) {
    const sparkle = new ResourceGeoParticle(position.clone(), RESOURCE_TYPES[resourceType].color, resourceType, 0.3 + Math.random() * 0.3);
    sparkle.velocity.multiplyScalar(0.2);
    sparkle.size *= 0.3;
    resourceSmokeParticles.push(sparkle);
  }
}

function updateResourceUI() {
  document.getElementById('coal-count').textContent = resourceInventory.Coal;
  document.getElementById('stone-count').textContent = resourceInventory.Stone;
  document.getElementById('gold-count').textContent = resourceInventory.Gold;
  document.getElementById('emerald-count').textContent = resourceInventory.Emerald;
}

function calculateSDFGradient(position) {
  const e = 0.1, grad = new THREE.Vector3();
  grad.x = sampleSDFAtPosition(new THREE.Vector3(position.x+e, position.y, position.z)) - sampleSDFAtPosition(new THREE.Vector3(position.x-e, position.y, position.z));
  grad.y = sampleSDFAtPosition(new THREE.Vector3(position.x, position.y+e, position.z)) - sampleSDFAtPosition(new THREE.Vector3(position.x, position.y-e, position.z));
  grad.z = sampleSDFAtPosition(new THREE.Vector3(position.x, position.y, position.z+e)) - sampleSDFAtPosition(new THREE.Vector3(position.x, position.y, position.z-e));
  return grad.normalize();
}

function enableResourceRadar() {
  const applyXray = (mesh, type, id) => {
    if (!resourceRadarMaterials.has(id)) resourceRadarMaterials.set(id, mesh.material);
    const xrayMat = new THREE.MeshBasicMaterial({ color: RESOURCE_TYPES[type].color, transparent: true, opacity: 0.8, depthTest: false, depthWrite: false, side: THREE.DoubleSide });
    mesh.material = xrayMat;
    mesh.renderOrder = 1000;
  };
  resourceNodes.forEach((res, key) => { if (res.active && res.mesh) applyXray(res.mesh, res.type, key); });
  resourceChunks.forEach(chunk => { if (chunk.mesh) applyXray(chunk.mesh, chunk.resourceType, chunk.mesh.uuid); });
}

function disableResourceRadar() {
  const restoreMat = (mesh, id) => {
    const origMat = resourceRadarMaterials.get(id);
    if (origMat) { mesh.material = origMat; mesh.renderOrder = 0; }
  };
  resourceNodes.forEach((res, key) => { if (res.active && res.mesh) restoreMat(res.mesh, key); });
  resourceChunks.forEach(chunk => { if (chunk.mesh) restoreMat(chunk.mesh, chunk.mesh.uuid); });
  resourceRadarMaterials.clear();
}

// ====================================================================================
// ====================================================================================
// END OF MODULE: resourceMiningSystem.js
// ====================================================================================
// ====================================================================================



// ====================================================================================
// ====================================================================================
// MODULE: playerController.js
// PURPOSE: Handles all player logic: position, movement (walking, jumping), camera
// controls, physics, and collision detection/response with the environment.
// ====================================================================================
// ====================================================================================

// --- Player State ---
let playerPosition = new THREE.Vector3(0, planetRadius + playerHeight, 0);
let radialDistance = planetRadius + playerHeight;
let verticalVelocity = 0;
let yaw = 0, pitch = 0;
let tangentVelocity = new THREE.Vector3(0, 0, 0);
let thirdPerson = false;

// --- Tunnel Physics State ---
let isInTunnel = false;
let tunnelVelocity = new THREE.Vector3(0, 0, 0);
let lastSurfaceNormal = new THREE.Vector3(0, 1, 0);
let tunnelModeTimer = 0;
const tunnelDetectionRadius = planetRadius * 0.3; // Distance from center to consider "in tunnel"
const tunnelExitRadius = planetRadius * 0.8; // Distance from center to exit tunnel mode

// --- Collision Smoothing State ---
let collisionHistory = [];
let lastCollisionNormal = new THREE.Vector3();
let velocityHistory = [];
let stableFrameCount = 0;
let lastPlayerPosition = new THREE.Vector3();

/**
 * Detects if the player is inside a tunnel (close to planet center with open space)
 * @param {THREE.Vector3} playerPos - Current player position
 * @returns {boolean} - True if player is in a tunnel
 */
function detectTunnelMode(playerPos) {
    const distanceFromCenter = playerPos.length();

    // If player is far from center, definitely not in tunnel
    if (distanceFromCenter > tunnelExitRadius) {
        return false;
    }

    // If player is very close to center, check if there's open space around
    if (distanceFromCenter < tunnelDetectionRadius) {
        // Sample multiple directions to see if there's open space
        const directions = [
            new THREE.Vector3(1, 0, 0),
            new THREE.Vector3(-1, 0, 0),
            new THREE.Vector3(0, 1, 0),
            new THREE.Vector3(0, -1, 0),
            new THREE.Vector3(0, 0, 1),
            new THREE.Vector3(0, 0, -1)
        ];

        let openDirections = 0;
        for (const dir of directions) {
            const testPoint = playerPos.clone().add(dir.clone().multiplyScalar(playerRadius * 2));
            const sdf = sampleSDF(testPoint);
            if (sdf > 0) { // Open space
                openDirections++;
            }
        }

        // If at least 2 directions are open, consider it a tunnel
        return openDirections >= 2;
    }

    return false;
}

/**
 * Calculates adaptive gravity based on player position relative to planet center.
 * Gravity direction shifts based on player location to create more realistic planetary physics.
 * @param {THREE.Vector3} playerPos - Current player position
 * @returns {number} - Adjusted gravity value
 */
function calculateAdaptiveGravity(playerPos) {
    // Normalize player position to get direction from center
    const playerDir = playerPos.clone().normalize();

    // Define cardinal directions (North, South, East, West)
    const north = new THREE.Vector3(0, 1, 0);
    const south = new THREE.Vector3(0, -1, 0);
    const east = new THREE.Vector3(1, 0, 0);
    const west = new THREE.Vector3(-1, 0, 0);

    // Calculate how much the player is aligned with each cardinal direction
    const northAlignment = Math.max(0, playerDir.dot(north));
    const southAlignment = Math.max(0, playerDir.dot(south));
    const eastAlignment = Math.max(0, playerDir.dot(east));
    const westAlignment = Math.max(0, playerDir.dot(west));

    // Base gravity strength
    let gravityStrength = gravity;

    // Adjust gravity based on position - gravity pulls toward opposite hemisphere
    // When in north, gravity pulls toward south (center-south direction)
    // When in south, gravity pulls toward north (center-north direction)
    // This creates a more realistic planetary gravity field

    if (northAlignment > 0.3) {
        // Player is in northern hemisphere, gravity should pull toward center-south
        gravityStrength *= (0.8 + 0.4 * northAlignment);
    } else if (southAlignment > 0.3) {
        // Player is in southern hemisphere, gravity should pull toward center-north
        gravityStrength *= (0.8 + 0.4 * southAlignment);
    } else if (eastAlignment > 0.3) {
        // Player is in eastern hemisphere, gravity should pull toward center-west
        gravityStrength *= (0.8 + 0.4 * eastAlignment);
    } else if (westAlignment > 0.3) {
        // Player is in western hemisphere, gravity should pull toward center-east
        gravityStrength *= (0.8 + 0.4 * westAlignment);
    }

    return gravityStrength;
}

/**
 * Main update function for the player, called every frame.
 * @param {number} delta - The time elapsed since the last frame.
 */
function updatePlayer(delta) {
    // --- Detect Tunnel Mode ---
    const wasInTunnel = isInTunnel;
    isInTunnel = detectTunnelMode(playerPosition);

    if (isInTunnel && !wasInTunnel) {
        // Entering tunnel mode
        console.log("Entering tunnel mode");
        tunnelModeTimer = 0;
        // Convert radial velocity to 3D velocity
        const currentDistance = Math.max(playerPosition.length(), 0.1);
        const radialDir = playerPosition.clone().divideScalar(currentDistance);
        tunnelVelocity.copy(radialDir.multiplyScalar(verticalVelocity));
        tunnelVelocity.add(tangentVelocity);
    } else if (!isInTunnel && wasInTunnel) {
        // Exiting tunnel mode
        console.log("Exiting tunnel mode");
        // Convert 3D velocity back to radial system
        const currentDistance = Math.max(playerPosition.length(), 0.1);
        const radialDir = playerPosition.clone().divideScalar(currentDistance);
        verticalVelocity = tunnelVelocity.dot(radialDir);
        tangentVelocity.copy(tunnelVelocity.clone().projectOnPlane(radialDir));
        radialDistance = currentDistance;
    }

    if (isInTunnel) {
        updatePlayerTunnelMode(delta);
    } else {
        updatePlayerSurfaceMode(delta);
    }
}

/**
 * Updates player physics when in tunnel mode (near planet center)
 */
function updatePlayerTunnelMode(delta) {
    tunnelModeTimer += delta;

    // --- 3D Movement in Tunnel ---
    if (keyStates['Space']) {
        // Jump/thrust in current up direction
        const currentUp = lastSurfaceNormal.clone();
        tunnelVelocity.add(currentUp.multiplyScalar(jumpStrength * 0.5 * delta));
    }

    // --- Apply gravity toward nearest surface ---
    const nearestSurface = findNearestSurface(playerPosition);
    if (nearestSurface) {
        const gravityDir = nearestSurface.normal.clone();
        const gravityStrength = Math.min(gravity, gravity * (playerPosition.length() / planetRadius));
        tunnelVelocity.add(gravityDir.multiplyScalar(gravityStrength * delta));
    }

    // --- Air resistance in tunnel ---
    tunnelVelocity.multiplyScalar(0.98);

    // --- Apply movement input ---
    const input = getTunnelMovementInput(delta);
    if (input.lengthSq() > 0) {
        tunnelVelocity.add(input.multiplyScalar(acceleration * delta * 0.7));
    }

    // --- Limit speed ---
    if (tunnelVelocity.length() > maxSpeed * 1.5) {
        tunnelVelocity.setLength(maxSpeed * 1.5);
    }

    // --- Apply velocity and check collision ---
    const newPos = playerPosition.clone().add(tunnelVelocity.clone().multiplyScalar(delta));
    const col = checkTunnelCollision(newPos);

    if (!col.collides) {
        playerPosition.copy(newPos);
    } else {
        // Bounce off walls
        const bounceVel = tunnelVelocity.clone().reflect(col.avgNormal).multiplyScalar(0.3);
        tunnelVelocity.copy(bounceVel);
        playerPosition.add(col.avgNormal.multiplyScalar(col.maxPenetration + 0.1));
    }

    // --- Update camera for tunnel mode ---
    updateTunnelCamera(delta);
}

/**
 * Updates player physics when on planet surface (traditional mode)
 */
function updatePlayerSurfaceMode(delta) {
    // --- Vertical Movement (Jumping & Gravity) ---
    if (keyStates['Space'] && verticalVelocity === 0) verticalVelocity = jumpStrength;
    const newRad = radialDistance + verticalVelocity * delta;

    // --- Adaptive Gravity Based on Player Position ---
    const adaptiveGravity = calculateAdaptiveGravity(playerPosition);
    verticalVelocity -= adaptiveGravity * delta;

    const currentDistance = Math.max(playerPosition.length(), 0.1);
    const dir = playerPosition.clone().divideScalar(currentDistance);
    const testPos = dir.clone().multiplyScalar(newRad);
    const col = checkCollision(testPos, dir);

    if (!col.collides) {
        radialDistance = newRad;
    } else {
        let corr = col.maxPenetration * (col.maxPenetration > collisionTolerance * 2 ? 0.8 : 0.4);
        if (corr < 0.01) corr *= 0.5;
        if (col.avgNormal.dot(dir) > 0) radialDistance += corr;
        if (col.isGrounded && verticalVelocity < 0) verticalVelocity = 0;
        else verticalVelocity *= velocityDamping;
    }
    playerPosition.setLength(radialDistance);

    // --- Camera Orientation ---
    const surfaceNormal = playerPosition.clone().normalize();
    lastSurfaceNormal.copy(surfaceNormal); // Store for tunnel mode
    const qAlign = new THREE.Quaternion().setFromUnitVectors(new THREE.Vector3(0,1,0), surfaceNormal);
    const qYaw = new THREE.Quaternion().setFromAxisAngle(surfaceNormal, yaw);
    const localRight = new THREE.Vector3(1,0,0).applyQuaternion(qAlign).applyQuaternion(qYaw);
    const qPitch = new THREE.Quaternion().setFromAxisAngle(localRight, pitch);
    const cameraQuat = qPitch.multiply(qYaw).multiply(qAlign);

    // --- Horizontal Movement (Walking) ---
    const forward = new THREE.Vector3(0,0,-1).applyQuaternion(cameraQuat);
    const rightV = new THREE.Vector3(1,0,0).applyQuaternion(cameraQuat);
    const forwardPlane = forward.projectOnPlane(surfaceNormal).normalize();
    const rightPlane = rightV.projectOnPlane(surfaceNormal).normalize();

    // Fix movement direction consistency across all planet regions
    // Ensure forward/backward and left/right directions are properly orthogonal
    const input = new THREE.Vector3();
    if (keyStates['KeyW']) {
        // Move forward - ensure consistent direction regardless of planet position
        const forwardDir = forwardPlane.clone().negate();
        // Normalize to prevent accumulation of floating point errors
        if (forwardDir.lengthSq() > 0) {
            input.add(forwardDir.normalize());
        }
    }
    if (keyStates['KeyS']) {
        // Move backward
        const backwardDir = forwardPlane.clone();
        if (backwardDir.lengthSq() > 0) {
            input.add(backwardDir.normalize());
        }
    }
    if (keyStates['KeyA']) {
        // Move left
        const leftDir = rightPlane.clone();
        if (leftDir.lengthSq() > 0) {
            input.add(leftDir.normalize());
        }
    }
    if (keyStates['KeyD']) {
        // Move right
        const rightDir = rightPlane.clone().negate();
        if (rightDir.lengthSq() > 0) {
            input.add(rightDir.normalize());
        }
    }

    if (input.lengthSq() > 0) {
        tangentVelocity.add(input.normalize().multiplyScalar(acceleration * delta));
    } else {
        tangentVelocity.multiplyScalar(Math.max(0, tangentVelocity.length() - friction * delta) / (tangentVelocity.length() || 1));
    }
    if (tangentVelocity.length() > maxSpeed) tangentVelocity.setLength(maxSpeed);
    if (stableFrameCount < 5) tangentVelocity.multiplyScalar(0.95);

    // --- Apply Tangential Velocity & Collide ---
    if (tangentVelocity.lengthSq() > 0) {
        const disp = tangentVelocity.clone().multiplyScalar(delta);
        const axis = new THREE.Vector3().crossVectors(disp, surfaceNormal).normalize();
        const angle = disp.length() / radialDistance;
        const qm = new THREE.Quaternion().setFromAxisAngle(axis, angle);
        const testP = playerPosition.clone().applyQuaternion(qm).setLength(radialDistance);
        const c2 = checkCollision(testP, testP.clone().normalize());

        if (!c2.collides || c2.isWalkableSlope) {
            playerPosition.applyQuaternion(qm);
            tangentVelocity.applyQuaternion(qm);
        } else if (c2.isSmallWall && tangentVelocity.length() <= 2.0) {
            playerPosition.applyQuaternion(qm);
            tangentVelocity.applyQuaternion(qm).multiplyScalar(0.5);
        } else {
            const tn = c2.avgNormal.clone().projectOnPlane(surfaceNormal).normalize();
            const vin = tangentVelocity.dot(tn);
            if (vin > 0) tangentVelocity.add(tn.multiplyScalar(-vin * 0.6));
            tangentVelocity.multiplyScalar(velocityDamping);
        }
        playerPosition.setLength(radialDistance);
    }

    // --- Update Camera Position ---
    if (thirdPerson) {
        const camOff = forwardPlane.clone().multiplyScalar(-3.3).add(surfaceNormal.clone().multiplyScalar(1.3));
        camera.position.copy(playerPosition.clone().add(camOff));
        camera.lookAt(playerPosition);
    } else {
        camera.position.copy(playerPosition);
        camera.quaternion.copy(cameraQuat);
    }
}

/**
 * Gets movement input for tunnel mode
 */
function getTunnelMovementInput(delta) {
    const input = new THREE.Vector3();

    // Use camera orientation for movement in tunnel
    const forward = new THREE.Vector3(0, 0, -1).applyQuaternion(camera.quaternion);
    const right = new THREE.Vector3(1, 0, 0).applyQuaternion(camera.quaternion);
    const up = new THREE.Vector3(0, 1, 0).applyQuaternion(camera.quaternion);

    if (keyStates['KeyW']) input.add(forward);
    if (keyStates['KeyS']) input.sub(forward);
    if (keyStates['KeyA']) input.sub(right);
    if (keyStates['KeyD']) input.add(right);

    return input.normalize();
}

/**
 * Finds the nearest surface to the player position
 */
function findNearestSurface(playerPos) {
    const directions = [
        new THREE.Vector3(1, 0, 0),
        new THREE.Vector3(-1, 0, 0),
        new THREE.Vector3(0, 1, 0),
        new THREE.Vector3(0, -1, 0),
        new THREE.Vector3(0, 0, 1),
        new THREE.Vector3(0, 0, -1)
    ];

    let nearestDistance = Infinity;
    let nearestNormal = null;

    for (const dir of directions) {
        for (let dist = 1; dist < 10; dist += 0.5) {
            const testPoint = playerPos.clone().add(dir.clone().multiplyScalar(dist));
            const sdf = sampleSDF(testPoint);
            if (sdf < 0) {
                if (dist < nearestDistance) {
                    nearestDistance = dist;
                    nearestNormal = calculateSurfaceNormal(testPoint);
                }
                break;
            }
        }
    }

    return nearestNormal ? { normal: nearestNormal, distance: nearestDistance } : null;
}

/**
 * Checks collision for tunnel mode (simpler 3D collision)
 */
function checkTunnelCollision(testPos) {
    let maxPen = 0;
    let hasCol = false;
    const normals = [];

    // Check collision at multiple points around player
    for (let i = 0; i < 8; i++) {
        const angle = (i / 8) * Math.PI * 2;
        const offset = new THREE.Vector3(
            Math.cos(angle) * playerRadius,
            Math.sin(angle) * playerRadius,
            0
        );

        const checkPoint = testPos.clone().add(offset);
        const sdf = sampleSDF(checkPoint);

        if (sdf < 0) {
            hasCol = true;
            const penetration = Math.abs(sdf);
            if (penetration > maxPen) {
                maxPen = penetration;
            }
            normals.push(calculateSurfaceNormal(checkPoint));
        }
    }

    // Average the normals
    let avgNormal = new THREE.Vector3(0, 1, 0);
    if (normals.length > 0) {
        avgNormal.set(0, 0, 0);
        normals.forEach(n => avgNormal.add(n));
        avgNormal.normalize();
    }

    return {
        collides: hasCol,
        maxPenetration: maxPen,
        avgNormal: avgNormal
    };
}

/**
 * Updates camera for tunnel mode
 */
function updateTunnelCamera(delta) {
    // Smooth camera orientation in tunnel
    const targetUp = lastSurfaceNormal.clone();

    // Allow free look in tunnel mode
    const qYaw = new THREE.Quaternion().setFromAxisAngle(targetUp, yaw);
    const localRight = new THREE.Vector3(1, 0, 0).applyQuaternion(qYaw);
    const qPitch = new THREE.Quaternion().setFromAxisAngle(localRight, pitch);
    const cameraQuat = qPitch.multiply(qYaw);

    if (thirdPerson) {
        const backOffset = new THREE.Vector3(0, 0, 3).applyQuaternion(cameraQuat);
        camera.position.copy(playerPosition.clone().add(backOffset));
        camera.lookAt(playerPosition);
    } else {
        camera.position.copy(playerPosition);
        camera.quaternion.copy(cameraQuat);
    }
}

/**
 * Checks for collision between the player capsule and the environment.
 * @param {THREE.Vector3} testPos - The potential new position of the player.
 * @param {THREE.Vector3} upDir - The player's current up direction.
 * @returns {object} A collision result object.
 */
function checkCollision(testPos, upDir) {
    if (polygonalMeshEnabled && polygonalPhysicsData && usePolygonalPhysics) {
        return checkCollisionWithPolygonalMesh(testPos, upDir);
    }

    let maxPen=0, hasCol=false, isGr=false, wallHeight=0;
    const wns=[];
    for (let h=0; h<=1; h++) {
        const cp = testPos.clone().add(upDir.clone().multiplyScalar(h * playerHeight - playerHeight));
        for (let i=0; i<collisionSamples; i++) {
            let tempRight = Math.abs(upDir.dot(new THREE.Vector3(1,0,0))) > 0.9 ? new THREE.Vector3(0,1,0) : new THREE.Vector3(1,0,0);
            const localRight = tempRight.cross(upDir).normalize();
            const localForward = upDir.clone().cross(localRight);
            const angle = (i/collisionSamples)*Math.PI*2;
            const offset = localRight.clone().multiplyScalar(Math.cos(angle)*playerRadius).add(localForward.multiplyScalar(Math.sin(angle)*playerRadius));
            const sd = sampleSDF(cp.clone().add(offset));
            if (sd < -collisionTolerance) {
                hasCol=true;
                const pen=-sd-collisionTolerance;
                maxPen=Math.max(maxPen,pen);
                wns.push({normal:calculateSurfaceNormal(cp.clone().add(offset)), weight:pen});
                if(h===0) isGr=true;
                if(h>0) wallHeight=Math.max(wallHeight, h * playerHeight);
            }
        }
    }

    let avgN=new THREE.Vector3(), totalW=0;
    wns.forEach(w => { avgN.add(w.normal.clone().multiplyScalar(w.weight)); totalW+=w.weight; });
    if(totalW>0) avgN.divideScalar(totalW).normalize();

    const isWalk = avgN.dot(upDir) > maxWalkableSlope;
    const isSmall = maxPen < smallWallThreshold && wallHeight < maxClimbableHeight && !isWalk;
    
    return smoothCollisionResponse({ collides:hasCol, maxPenetration:Math.min(maxPen,maxCorrectionPerFrame), avgNormal:avgN, isGrounded:isGr, isWalkableSlope:isWalk, isSmallWall:isSmall, wallHeight:wallHeight });
}

function checkCollisionWithPolygonalMesh(testPos, upDir) {
    if (!polygonalPhysicsData) return { collides: false };

    let maxPen = 0, hasCol = false, isGr = false, wallHeight = 0;
    const collisionNormals = [], penetrations = [];

    for (let h = 0; h <= 1; h++) {
        const cp = testPos.clone().add(upDir.clone().multiplyScalar(h * playerHeight - playerHeight));
        for (let i = 0; i < collisionSamples * 2; i++) {
            let tempRight = Math.abs(upDir.dot(new THREE.Vector3(1,0,0))) > 0.9 ? new THREE.Vector3(0,1,0) : new THREE.Vector3(1,0,0);
            const localRight = tempRight.cross(upDir).normalize();
            const localForward = upDir.clone().cross(localRight);
            const angle = (i / (collisionSamples * 2)) * Math.PI * 2;
            const offset = localRight.multiplyScalar(Math.cos(angle)*playerRadius).add(localForward.multiplyScalar(Math.sin(angle)*playerRadius));
            const samplePoint = cp.clone().add(offset);

            getNearbyTriangles(samplePoint, polygonalPhysicsData).forEach(triangle => {
                const collision = checkPointTriangleCollision(samplePoint, triangle);
                if (collision.collides) {
                    hasCol = true;
                    maxPen = Math.max(maxPen, collision.penetration);
                    collisionNormals.push(collision.normal);
                    penetrations.push(collision.penetration);
                    if (Math.abs(collision.normal.dot(upDir)) > maxWalkableSlope && h === 0) isGr = true;
                    if (Math.abs(collision.normal.dot(upDir)) <= maxWalkableSlope && h > 0) wallHeight = Math.max(wallHeight, h * playerHeight);
                }
            });
        }
    }

    let avgNormal = new THREE.Vector3(0, 1, 0);
    if (collisionNormals.length > 0) {
        avgNormal.set(0, 0, 0);
        let totalWeight = 0;
        collisionNormals.forEach((n, i) => { avgNormal.add(n.clone().multiplyScalar(penetrations[i])); totalWeight += penetrations[i]; });
        if (totalWeight > 0) avgNormal.divideScalar(totalWeight).normalize();
    }

    const isWalk = avgNormal.dot(upDir) > maxWalkableSlope;
    const isSmall = maxPen < smallWallThreshold && wallHeight < maxClimbableHeight && !isWalk;
    return smoothCollisionResponse({ collides: hasCol, maxPenetration: Math.min(maxPen, maxCorrectionPerFrame), avgNormal, isGrounded: isGr, isWalkableSlope: isWalk, isSmallWall: isSmall, wallHeight });
}

function smoothCollisionResponse(collision) {
    if (playerPosition.distanceTo(lastPlayerPosition) > positionChangeThreshold) {
        collisionHistory = []; velocityHistory = []; stableFrameCount = 0;
    }
    lastPlayerPosition.copy(playerPosition);
    collisionHistory.push({ ...collision, timestamp: performance.now(), position: playerPosition.clone() });
    if (collisionHistory.length > collisionHistorySize) collisionHistory.shift();
    velocityHistory.push(tangentVelocity.length());
    if (velocityHistory.length > velocityHistorySize) velocityHistory.shift();
    if (velocityHistory.length < 3 || (velocityHistory.reduce((a,b)=>a+b,0)/velocityHistory.length < 0.5)) stableFrameCount++; else stableFrameCount=0;
    if (collisionHistory.length >= 2) {
        const recent=collisionHistory[collisionHistory.length-1], prev=collisionHistory[collisionHistory.length-2];
        if (recent.collides && prev.collides && recent.position.distanceTo(prev.position) < 2.0) {
            collision.maxPenetration = collision.maxPenetration * (stableFrameCount < 10 ? 0.6 : 0.8) + prev.maxPenetration * (1 - (stableFrameCount < 10 ? 0.6 : 0.8));
        }
    }
    return collision;
}

function getNearbyTriangles(point, physicsData) {
    const triangles = [], gridSize = physicsData.gridSize, grid = physicsData.spatialGrid;
    for (let dx = -1; dx <= 1; dx++) for (let dy = -1; dy <= 1; dy++) for (let dz = -1; dz <= 1; dz++) {
        const key = `${Math.floor(point.x/gridSize)+dx},${Math.floor(point.y/gridSize)+dy},${Math.floor(point.z/gridSize)+dz}`;
        if (grid.has(key)) triangles.push(...grid.get(key));
    }
    return triangles;
}

function checkPointTriangleCollision(point, triangle) {
    const toPoint = new THREE.Vector3().subVectors(point, triangle.vertices[0]);
    const distToPlane = toPoint.dot(triangle.normal);
    if (Math.abs(distToPlane) > playerRadius * 0.8) return { collides: false };
    const projectedPoint = point.clone().sub(triangle.normal.clone().multiplyScalar(distToPlane));
    const toProj = new THREE.Vector3().subVectors(projectedPoint, triangle.vertices[0]);
    const dot00 = triangle.edge2.dot(triangle.edge2), dot01 = triangle.edge2.dot(triangle.edge1), dot02 = triangle.edge2.dot(toProj);
    const dot11 = triangle.edge1.dot(triangle.edge1), dot12 = triangle.edge1.dot(toProj);
    const invDenom = 1 / (dot00 * dot11 - dot01 * dot01);
    const u = (dot11 * dot02 - dot01 * dot12) * invDenom, v = (dot00 * dot12 - dot01 * dot02) * invDenom;
    if (u >= 0 && v >= 0 && u + v <= 1) {
        return { collides: true, penetration: playerRadius * 0.8 - Math.abs(distToPlane), normal: triangle.normal.clone(), point: projectedPoint };
    }
    return { collides: false };
}

// ====================================================================================
// ====================================================================================
// END OF MODULE: playerController.js
// ====================================================================================
// ====================================================================================



// ====================================================================================
// ====================================================================================
// MODULE: main.js
// PURPOSE: The main entry point of the application. It initializes all other modules
// in the correct order and runs the main animation loop.
// ====================================================================================
// ====================================================================================

let prevTime = 0;

/**
 * Initializes the entire application.
 */
function init() {
  updateLoadingProgress(0, 'Initializing scene...');
  initScene();
  initLights();
  initInputListeners();
  initMiningParticleSystem();

  setTimeout(() => {
    initTerrain();
    initFlashlight();
    setTimeout(() => hideLoadingScreen(), 500);
  }, 100);

  prevTime = performance.now();
  lastTerrainUpdate = prevTime;
  animate();
}

/**
 * The main update function, called every frame by animate().
 * @param {number} delta - The time elapsed since the last frame.
 */
function update(delta) {
    updatePlayer(delta);
    updateFlashlight(delta);
    updateFrustumCulling();
    processChunkUpdateQueue();
    handleContinuousSculpting();
    if (polygonalMeshEnabled) updatePolygonalMeshIfNeeded();
    updateResourceSystem(delta);
    updateClimbStatus();
    if (frameCount % 30 === 0) updatePerformanceDisplay();
    const statusElement = document.getElementById('terrain-status');
    if (statusElement && chunkUpdateQueue.size === 0 && !isWorkerBusy) {
        statusElement.textContent = '100';
        statusElement.className = 'perf-good';
    }
}

/**
 * The main animation loop.
 */
function animate() {
  requestAnimationFrame(animate);
  const now = performance.now();
  const delta = (now - prevTime) / 1000;
  prevTime = now;

  update(delta);
  updatePerformanceMonitor(delta);
  updateMiningParticles(delta);

  renderer.render(scene, camera);
}

// Start the application once the DOM is loaded.
window.addEventListener('DOMContentLoaded', init);

// ====================================================================================
// ====================================================================================
// END OF MODULE: main.js
// ====================================================================================
// ====================================================================================