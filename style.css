html, body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  height: 100%;
  background-color: #000;
  font-family: sans-serif;
  color: #fff;
}

#info {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 12px;
  border-radius: 4px;
  pointer-events: none;
}

/* Performance Monitor Styles */
#perf-monitor {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.6);
    padding: 10px;
    border-radius: 5px;
    color: white;
    font-family: monospace;
    font-size: 12px;
    z-index: 100;
    min-width: 150px;
}

#perf-monitor div {
    margin-bottom: 4px;
}

#perf-monitor span {
    font-weight: bold;
}

.perf-good { color: #4CAF50; }
.perf-warning { color: #FFC107; }
.perf-bad { color: #F44336; }

/* Resource Panel Styling */
#resource-panel {
  position: fixed;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid #444;
  border-radius: 8px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  color: #fff;
  min-width: 180px;
  z-index: 1000;
}

#resource-panel h3 {
  margin: 0 0 10px 0;
  color: #4CAF50;
  text-align: center;
  font-size: 16px;
  border-bottom: 1px solid #444;
  padding-bottom: 5px;
}

.resource-item {
  display: flex;
  align-items: center;
  margin: 8px 0;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.resource-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.resource-icon {
  font-size: 16px;
  margin-right: 8px;
  width: 20px;
  text-align: center;
}

.coal-icon { filter: brightness(0.7); }
.stone-icon { filter: brightness(0.8); }
.gold-icon { filter: brightness(1.2) saturate(1.5); }
.emerald-icon { filter: brightness(1.1) saturate(1.3); }

.resource-name {
  flex: 1;
  font-size: 14px;
  color: #ccc;
}

.resource-count {
  font-weight: bold;
  font-size: 14px;
  color: #4CAF50;
  min-width: 30px;
  text-align: right;
}